﻿<PERSON>erasik
<EMAIL>
June 16, 2025
Ok, ale wydaje mi się, że programista nie będzie wiedział o co chodzi. Rozbuduj ten prompt wyjaś<PERSON>jąc wszystkie funkcje aplik<PERSON>ji, co ma rob<PERSON>, j<PERSON>, wszelkie opisy itd. Długi obszerny prompt wdrożenia takiej aplikacji na VPS od A do Z.
Kompletny Prompt dla AI Programisty - System AI Brand Monitor
WPROWADZENIE I CEL PROJEKTU
Twoim zadaniem jest stworzenie od podstaw kompletnego systemu o nazwie AI Brand Monitor - zaawansowanej platformy do monitorowania i optymalizacji widoczności marek w chatbotach AI (ChatGPT, Claude, Perplexity, Bard). System ma umożliwiać firmom śledzenie jak ich marka jest prezentowana przez AI, porównywanie się z konkurencją oraz otrzymywanie rekomendacji poprawy widoczności.
KONTEKST BIZNESOWY W 2025 roku coraz więcej użytkowników korzysta z chatbotów AI zamiast tradycyjnych wyszukiwarek. Firmy potrzebują narzędzia do monitorowania swojej reputacji i widoczności w odpowiedziach AI, podobnie jak kiedyś monitorowały pozycje w Google. Twój system ma wypełnić tę lukę rynkową.

SZCZEGÓŁOWA SPECYFIKACJA FUNKCJONALNA
1. Monitorowanie Marek
- System automatycznie zadaje chatbotom AI różnorodne pytania związane z marką klienta
- Przykłady zapytań: "Jakie są najlepsze [produkt] firmy [marka]?", "Porównaj [marka] z konkurencją", "Czy warto kupić [produkt] od [marka]?"
- Zapisuje pełne odpowiedzi AI wraz z metadanymi (data, platforma, wersja modelu)
- Analizuje kontekst i sentyment każdej wzmianki
2. Analiza Konkurencji
- Porównuje widoczność marki klienta z konkurentami
- Tworzy ranking "share of voice" w odpowiedziach AI
- Identyfikuje mocne strony konkurencji wymieniane przez AI
3. System Scoringu
- Visibility Score (0-100) - ogólna widoczność marki
- Sentiment Score (-100 do +100) - jak pozytywnie AI mówi o marce
- Authority Score (0-100) - czy AI uznaje markę za lidera w branży
- Trend Score - czy widoczność rośnie czy spada
4. Rekomendacje Optymalizacji
- Sugestie treści do publikacji aby poprawić widoczność
- Analiza słów kluczowych najczęściej kojarzonych z marką
- Identyfikacja luk informacyjnych (czego AI nie wie o marce)

KROK 1: PRZYGOTOWANIE ŚRODOWISKA I STRUKTURY PROJEKTU
Zadanie: Stwórz kompletną strukturę projektu z wszystkimi niezbędnymi plikami i konfiguracją.
ai-brand-monitor/
├── app/
│   ├── __init__.py
│   ├── main.py                 # Główny punkt wejścia aplikacji
│   ├── config.py              # Konfiguracja (API keys, settings)
│   ├── database/
│   │   ├── __init__.py
│   │   ├── models.py          # Modele SQLAlchemy dla SQLite
│   │   ├── database.py        # Połączenie z bazą
│   │   └── migrations.py      # Skrypty migracyjne
│   ├── scrapers/
│   │   ├── __init__.py
│   │   ├── base_scraper.py   # Abstrakcyjna klasa bazowa
│   │   ├── chatgpt_scraper.py
│   │   ├── claude_scraper.py
│   │   ├── perplexity_scraper.py
│   │   └── query_generator.py # Generator inteligentnych zapytań
│   ├── analyzers/
│   │   ├── __init__.py
│   │   ├── nlp_analyzer.py   # Analiza NLP odpowiedzi
│   │   ├── sentiment.py      # Analiza sentymentu
│   │   ├── scoring.py        # System punktacji
│   │   └── competitor.py     # Analiza konkurencji
│   ├── api/
│   │   ├── __init__.py
│   │   ├── routes.py         # Endpointy FastAPI
│   │   ├── auth.py           # JWT authentication
│   │   └── schemas.py        # Pydantic models
│   ├── web/
│   │   ├── __init__.py
│   │   ├── dashboard.py      # Streamlit dashboard
│   │   └── templates/        # HTML templates
│   ├── optimization/
│   │   ├── __init__.py
│   │   ├── recommendations.py # Engine rekomendacji
│   │   └── content_analyzer.py
│   ├── notifications/
│   │   ├── __init__.py
│   │   ├── email_sender.py
│   │   └── webhooks.py
│   └── utils/
│       ├── __init__.py
│       ├── logger.py         # Centralne logowanie
│       └── helpers.py        # Funkcje pomocnicze
├── data/
│   ├── brand_monitor.db      # Baza SQLite
│   └── exports/              # Eksportowane raporty
├── logs/
├── tests/
├── static/                   # Pliki statyczne dla web
├── .env.example             # Przykładowy plik środowiskowy
├── requirements.txt
├── docker-compose.yml
├── Dockerfile
├── setup.py
└── README.md
Zawartość requirements.txt:
fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
sqlite3
selenium==4.15.0
playwright==1.40.0
beautifulsoup4==4.12.2
requests==2.31.0
spacy==3.7.2
textblob==0.17.1
pandas==2.1.3
numpy==1.26.2
scikit-learn==1.3.2
streamlit==1.29.0
plotly==5.18.0
apscheduler==3.10.4
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
email-validator==2.1.0
python-dotenv==1.0.0
pytest==7.4.3
black==23.11.0
flake8==6.1.0
loguru==0.7.2
tenacity==8.2.3
fake-useragent==1.4.0
cloudscraper==1.2.71
Zawartość config.py:
import os
from dotenv import load_dotenv
from pathlib import Path

load_dotenv()

class Config:
    # Paths
    BASE_DIR = Path(__file__).parent.parent
    DATA_DIR = BASE_DIR / "data"
    LOG_DIR = BASE_DIR / "logs"
    
    # Database
    DATABASE_URL = f"sqlite:///{DATA_DIR}/brand_monitor.db"
    
    # API Settings
    API_HOST = os.getenv("API_HOST", "0.0.0.0")
    API_PORT = int(os.getenv("API_PORT", 8000))
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    
    # Scraping Settings
    SCRAPING_DELAY = int(os.getenv("SCRAPING_DELAY", 5))  # seconds between requests
    MAX_RETRIES = 3
    USER_AGENTS_ROTATION = True
    
    # AI Platforms
    PLATFORMS = {
        "chatgpt": {
            "enabled": True,
            "base_url": "https://chat.openai.com",
            "requires_auth": True
        },
        "claude": {
            "enabled": True,
            "base_url": "https://claude.ai",
            "requires_auth": True
        },
        "perplexity": {
            "enabled": True,
            "base_url": "https://www.perplexity.ai",
            "requires_auth": False
        }
    }
    
    # Analysis Settings
    MIN_CONFIDENCE_SCORE = 0.7
    SENTIMENT_THRESHOLD = 0.1
    
    # Notifications
    SMTP_HOST = os.getenv("SMTP_HOST", "smtp.gmail.com")
    SMTP_PORT = int(os.getenv("SMTP_PORT", 587))
    SMTP_USER = os.getenv("SMTP_USER")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")
    
    # Scheduling
    SCAN_SCHEDULE = "0 9 * * *"  # Daily at 9 AM
    REPORT_SCHEDULE = "0 10 * * 1"  # Weekly on Monday at 10 AM

config = Config()
Nie zatrzymuj się, kontynuuj natychmiast do kroku 2.

KROK 2: IMPLEMENTACJA MODELU BAZY DANYCH
Zadanie: Stwórz kompletny model bazy danych SQLite z wszystkimi tabelami i relacjami.
Plik database/models.py:
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, ForeignKey, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from datetime import datetime
import json

Base = declarative_base()

class Brand(Base):
    __tablename__ = 'brands'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text)
    industry = Column(String(50))
    keywords = Column(JSON)  # Lista słów kluczowych do monitorowania
    competitors = Column(JSON)  # Lista nazw konkurentów
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relationships
    queries = relationship("Query", back_populates="brand", cascade="all, delete-orphan")
    mentions = relationship("Mention", back_populates="brand", cascade="all, delete-orphan")
    analytics = relationship("Analytics", back_populates="brand", cascade="all, delete-orphan")
    alerts = relationship("Alert", back_populates="brand", cascade="all, delete-orphan")

class Query(Base):
    __tablename__ = 'queries'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    query_text = Column(Text, nullable=False)
    query_type = Column(String(50))  # 'product_comparison', 'brand_info', 'recommendation', etc.
    platform = Column(String(50))  # 'chatgpt', 'claude', 'perplexity'
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    brand = relationship("Brand", back_populates="queries")
    responses = relationship("AIResponse", back_populates="query", cascade="all, delete-orphan")

class AIResponse(Base):
    __tablename__ = 'ai_responses'
    
    id = Column(Integer, primary_key=True)
    query_id = Column(Integer, ForeignKey('queries.id'), nullable=False)
    platform = Column(String(50), nullable=False)
    response_text = Column(Text, nullable=False)
    response_metadata = Column(JSON)  # model version, temperature, etc.
    response_time = Column(Float)  # Response time in seconds
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    query = relationship("Query", back_populates="responses")
    mentions = relationship("Mention", back_populates="response", cascade="all, delete-orphan")

class Mention(Base):
    __tablename__ = 'mentions'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    response_id = Column(Integer, ForeignKey('ai_responses.id'), nullable=False)
    mention_text = Column(Text)  # Exact text where brand is mentioned
    context = Column(Text)  # Surrounding context
    sentiment_score = Column(Float)  # -1 to 1
    confidence_score = Column(Float)  # 0 to 1
    position_in_response = Column(Integer)  # Where in response it appears
    is_primary_focus = Column(Boolean, default=False)  # Is brand the main topic?
    competitor_mentions = Column(JSON)  # Which competitors were also mentioned
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    brand = relationship("Brand", back_populates="mentions")
    response = relationship("AIResponse", back_populates="mentions")

class Analytics(Base):
    __tablename__ = 'analytics'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    date = Column(DateTime, nullable=False)
    visibility_score = Column(Float)  # 0-100
    sentiment_score = Column(Float)  # -100 to 100
    authority_score = Column(Float)  # 0-100
    share_of_voice = Column(Float)  # 0-100 percentage
    total_mentions = Column(Integer)
    positive_mentions = Column(Integer)
    negative_mentions = Column(Integer)
    neutral_mentions = Column(Integer)
    competitor_comparison = Column(JSON)  # Detailed competitor metrics
    top_keywords = Column(JSON)  # Most associated keywords
    platform_breakdown = Column(JSON)  # Metrics per platform
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    brand = relationship("Brand", back_populates="analytics")

class Alert(Base):
    __tablename__ = 'alerts'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    alert_type = Column(String(50))  # 'visibility_drop', 'negative_mention', 'competitor_surge'
    severity = Column(String(20))  # 'low', 'medium', 'high', 'critical'
    title = Column(String(200))
    message = Column(Text)
    data = Column(JSON)  # Additional context data
    is_read = Column(Boolean, default=False)
    is_resolved = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)
    
    # Relationships
    brand = relationship("Brand", back_populates="alerts")

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    email = Column(String(100), unique=True, nullable=False)
    hashed_password = Column(String(200), nullable=False)
    full_name = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
    
    # Relationships
    user_brands = relationship("UserBrand", back_populates="user", cascade="all, delete-orphan")

class UserBrand(Base):
    __tablename__ = 'user_brands'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    role = Column(String(50), default='viewer')  # 'viewer', 'editor', 'admin'
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="user_brands")
    brand = relationship("Brand")

class ScanHistory(Base):
    __tablename__ = 'scan_history'
    
    id = Column(Integer, primary_key=True)
    brand_id = Column(Integer, ForeignKey('brands.id'), nullable=False)
    scan_type = Column(String(50))  # 'manual', 'scheduled', 'api'
    status = Column(String(20))  # 'pending', 'running', 'completed', 'failed'
    queries_count = Column(Integer, default=0)
    responses_count = Column(Integer, default=0)
    mentions_count = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    
    brand = relationship("Brand")
Plik database/database.py:
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from contextlib import contextmanager
import os
from .models import Base
from app.config import config

# Create engine
engine = create_engine(
    config.DATABASE_URL,
    connect_args={"check_same_thread": False}  # Needed for SQLite
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Database dependency for FastAPI
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Context manager for database sessions
@contextmanager
def get_db_session():
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()

# Initialize database
def init_db():
    """Create all tables"""
    Base.metadata.create_all(bind=engine)
    print("Database initialized successfully!")

# Drop all tables (use with caution!)
def reset_db():
    """Drop all tables and recreate"""
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    print("Database reset successfully!")
Plik database/migrations.py:
from datetime import datetime
from .database import get_db_session, init_db
from .models import User, Brand
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_initial_data():
    """Create initial data for testing"""
    with get_db_session() as db:
        # Create admin user
        admin = User(
            email="<EMAIL>",
            hashed_password=pwd_context.hash("admin123"),
            full_name="System Admin",
            is_admin=True,
            is_active=True
        )
        db.add(admin)
        
        # Create sample brands
        brands = [
            Brand(
                name="Nike",
                description="Global sports apparel brand",
                industry="Sports & Fashion",
                keywords=["nike", "just do it", "nike shoes", "nike sneakers"],
                competitors=["Adidas", "Puma", "Under Armour", "New Balance"]
            ),
            Brand(
                name="Apple",
                description="Technology company",
                industry="Technology",
                keywords=["apple", "iphone", "macbook", "ipad", "apple products"],
                competitors=["Samsung", "Google", "Microsoft", "Sony"]
            )
        ]
        
        for brand in brands:
            db.add(brand)
        
        db.commit()
        print("Initial data created successfully!")

if __name__ == "__main__":
    init_db()
    create_initial_data()
Kontynuuj bez zatrzymywania do kroku 3.

KROK 3: IMPLEMENTACJA SYSTEMU SCRAPOWANIA AI
Zadanie: Stwórz zaawansowany system scrapowania odpowiedzi z różnych platform AI.
Plik scrapers/base_scraper.py:
from abc import ABC, abstractmethod
from typing import Dict, List, Optional
import time
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from fake_useragent import UserAgent
import logging
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)

class BaseScraper(ABC):
    """Abstract base class for AI platform scrapers"""
    
    def __init__(self, platform_name: str, headless: bool = True):
        self.platform_name = platform_name
        self.headless = headless
        self.ua = UserAgent()
        self.driver = None
        
    def setup_driver(self):
        """Setup Chrome driver with anti-detection measures"""
        options = Options()
        
        if self.headless:
            options.add_argument('--headless')
        
        # Anti-detection measures
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument(f'user-agent={self.ua.random}')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        # Additional privacy options
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-logging')
        options.add_argument('--log-level=3')
        
        self.driver = webdriver.Chrome(options=options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
    def close_driver(self):
        """Safely close the driver"""
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    def random_delay(self, min_seconds: float = 1, max_seconds: float = 3):
        """Add random delay to mimic human behavior"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
    
    def human_like_typing(self, element, text: str):
        """Type text with human-like delays"""
        element.clear()
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.2))
    
    @abstractmethod
    def login(self, credentials: Dict[str, str]) -> bool:
        """Login to the platform if required"""
        pass
    
    @abstractmethod
    def send_query(self, query: str) -> Optional[str]:
        """Send query and get response"""
        pass
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def scrape_response(self, query: str, credentials: Optional[Dict] = None) -> Dict:
        """Main method to scrape response with retry logic"""
        try:
            start_time = time.time()
            
            # Setup driver if not already done
            if not self.driver:
                self.setup_driver()
            
            # Login if credentials provided and required
            if credentials and hasattr(self, 'requires_login') and self.requires_login:
                login_success = self.login(credentials)
                if not login_success:
                    raise Exception("Login failed")
            
            # Send query and get response
            response_text = self.send_query(query)
            
            if not response_text:
                raise Exception("No response received")
            
            response_time = time.time() - start_time
            
            return {
                'platform': self.platform_name,
                'query': query,
                'response': response_text,
                'response_time': response_time,
                'timestamp': datetime.utcnow().isoformat(),
                'success': True,
                'error': None
            }
            
        except Exception as e:
            logger.error(f"Error scraping {self.platform_name}: {str(e)}")
            return {
                'platform': self.platform_name,
                'query': query,
                'response': None,
                'response_time': None,
                'timestamp': datetime.utcnow().isoformat(),
                'success': False,
                'error': str(e)
            }
Plik scrapers/chatgpt_scraper.py:
from .base_scraper import BaseScraper
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import time
import logging

logger = logging.getLogger(__name__)

class ChatGPTScraper(BaseScraper):
    """Scraper for ChatGPT responses"""
    
    def __init__(self, headless: bool = True):
        super().__init__("chatgpt", headless)
        self.base_url = "https://chat.openai.com"
        self.requires_login = True
    
    def login(self, credentials: Dict[str, str]) -> bool:
        """Login to ChatGPT"""
        try:
            self.driver.get(self.base_url)
            self.random_delay(2, 4)
            
            # Click login button
            login_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Log in')]"))
            )
            login_btn.click()
            self.random_delay()
            
            # Enter email
            email_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            self.human_like_typing(email_input, credentials['email'])
            
            # Click continue
            continue_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            continue_btn.click()
            self.random_delay()
            
            # Enter password
            password_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "password"))
            )
            self.human_like_typing(password_input, credentials['password'])
            
            # Submit
            submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            submit_btn.click()
            self.random_delay(3, 5)
            
            # Check if logged in successfully
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[@data-id='root']"))
            )
            
            logger.info("Successfully logged in to ChatGPT")
            return True
            
        except Exception as e:
            logger.error(f"ChatGPT login failed: {str(e)}")
            return False
    
    def send_query(self, query: str) -> Optional[str]:
        """Send query to ChatGPT and get response"""
        try:
            # Find the input textarea
            input_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//textarea[@data-id='root']"))
            )
            
            # Type the query
            self.human_like_typing(input_box, query)
            self.random_delay(0.5, 1)
            
            # Send the message
            input_box.send_keys(Keys.RETURN)
            
            # Wait for response to start generating
            self.random_delay(2, 3)
            
            # Wait for response to complete (look for stop generating button to disappear)
            WebDriverWait(self.driver, 60).until_not(
                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), 'Stop generating')]"))
            )
            
            # Get the last message (AI response)
            messages = self.driver.find_elements(By.XPATH, "//div[@data-message-author-role='assistant']")
            
            if messages:
                last_message = messages[-1]
                response_text = last_message.text
                return response_text
            
            return None
            
        except Exception as e:
            logger.error(f"Error sending query to ChatGPT: {str(e)}")
            return None
Plik scrapers/claude_scraper.py:
from .base_scraper import BaseScraper
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import logging

logger = logging.getLogger(__name__)

class ClaudeScraper(BaseScraper):
    """Scraper for Claude AI responses"""
    
    def __init__(self, headless: bool = True):
        super().__init__("claude", headless)
        self.base_url = "https://claude.ai"
        self.requires_login = True
    
    def login(self, credentials: Dict[str, str]) -> bool:
        """Login to Claude"""
        try:
            self.driver.get(self.base_url)
            self.random_delay(2, 4)
            
            # Click login button
            login_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Log in')]"))
            )
            login_btn.click()
            self.random_delay()
            
            # Enter email
            email_input = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[@type='email']"))
            )
            self.human_like_typing(email_input, credentials['email'])
            
            # Click continue
            continue_btn = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Continue with email')]")
            continue_btn.click()
            self.random_delay(2, 3)
            
            # Handle verification code or password
            # This part may vary based on Claude's auth flow
            
            logger.info("Successfully logged in to Claude")
            return True
            
        except Exception as e:
            logger.error(f"Claude login failed: {str(e)}")
            return False
    
    def send_query(self, query: str) -> Optional[str]:
        """Send query to Claude and get response"""
        try:
            # Find the input field
            input_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//div[@contenteditable='true']"))
            )
            
            # Click to focus


### References

1. **Jak pisać skuteczne prompty AI w ChatGPT? 90 przykładów! - widoczni**. [https://widoczni.com](https://widoczni.com/blog/najlepsze-prompty-chatgpt/)
2. **Jak pisać lepsze prompty w ChatGPT 2025? Sprawdź tych 13 zasad! - widoczni**. [https://widoczni.com](https://widoczni.com/blog/zasady-pisania-promptow/)
3. **Sztuka pytania: jak formułować skuteczne prompty?**. [https://www.sunrisesystem.pl](https://www.sunrisesystem.pl/blog/sztuka-pytania-jak-formulowac-skuteczne-prompty/)
4. **❌ Top 3️⃣ błędy w pisaniu promptów – na co uważać?**. [https://www.designpractice.pl](https://www.designpractice.pl/blog/top-3-bledy-w-pisaniu-promptow-na-co-uwazac)
5. **Skuteczne prompty ChatGPT. Jak pisać prompty dla AI? | scislak.com**. [https://scislak.com](https://scislak.com/blog/jak-pisac-prompty-dla-sztucznej-inteligencji/)
6. **Wprowadzenie do Prompt Engineering - Najlepsze prompty do narzędzi AI | Adrian Prędkiewicz**. [https://adrianpredkiewicz.pl](https://adrianpredkiewicz.pl/wprowadzenie-do-prompt-engineering-najlepsze-prompty-do-narzedzi-ai/)
7. **Prompty do ChatGPT: Jak pisać skuteczne polecenia**. [https://marcinkordowski.com](https://marcinkordowski.com/prompty-do-chatgpt-jak-pisac-skuteczne-polecenia/)
8. **Programista czy developer? Mini-słownik nazw stanowisk w branży IT**. [https://kariera.comarch.pl](https://kariera.comarch.pl/blog/programista-czy-developer-mini-slownik-nazw-stanowisk-w-branzy-it/)
9. **Jak tworzyć skuteczne prompty dla ChatGPT? Praktyczne wskazówki 🚀**. [https://www.cognity.pl](https://www.cognity.pl/blog-jak-tworzyc-polecenia-prompty-dla-chatgpt)
10. **Prompt co to jest? Kompletny przewodnik po promptach w 2025 roku - ᴘʀᴏᴍᴘᴛᴏᴡʏ 🤖**. [https://promptowy.com](https://promptowy.com/prompt-co-to-jest-kompletny-przewodnik-po-promptach-w-2025-roku/)
Hubert Gerasik
<EMAIL>
June 16, 2025
Kontynuuj
Plik analyzers/scoring.py (kontynuacja):
        # Index where 100 = same as competitors, >100 = better, <100 = worse
        competitive_index = (brand_score / avg_competitor_score) * 100
        
        return round(min(competitive_index, 200), 2)  # Cap at 200%
Plik analyzers/competitor.py:
from typing import Dict, List, Set
import re
from collections import defaultdict

class CompetitorAnalyzer:
    """Analyze competitor mentions and comparisons"""
    
    def __init__(self):
        self.comparison_patterns = [
            r'(\w+)\s+(?:is|are)\s+better\s+than\s+(\w+)',
            r'(\w+)\s+outperforms?\s+(\w+)',
            r'(\w+)\s+beats?\s+(\w+)',
            r'prefer\s+(\w+)\s+over\s+(\w+)',
            r'(\w+)\s+vs\.?\s+(\w+)',
            r'compared\s+to\s+(\w+),\s+(\w+)',
            r'(\w+)\s+superior\s+to\s+(\w+)',
            r'choose\s+(\w+)\s+instead\s+of\s+(\w+)'
        ]
    
    def analyze_competitor_mentions(self, responses: List[Dict], brand_name: str, competitors: List[str]) -> Dict:
        """Analyze how competitors are mentioned in responses"""
        competitor_data = {comp: {
            'mention_count': 0,
            'co_mentions': 0,  # Mentioned together with brand
            'comparison_wins': 0,
            'comparison_losses': 0,
            'contexts': []
        } for comp in competitors}
        
        brand_lower = brand_name.lower()
        
        for response in responses:
            text = response.get('response', '').lower()
            
            # Check each competitor
            for competitor in competitors:
                comp_lower = competitor.lower()
                
                # Count mentions
                mentions = len(re.findall(r'\b' + re.escape(comp_lower) + r'\b', text))
                competitor_data[competitor]['mention_count'] += mentions
                
                # Check co-mentions
                if comp_lower in text and brand_lower in text:
                    competitor_data[competitor]['co_mentions'] += 1
                    
                    # Analyze comparisons
                    comparison_result = self._analyze_comparison(text, brand_name, competitor)
                    if comparison_result == 'brand_wins':
                        competitor_data[competitor]['comparison_losses'] += 1
                    elif comparison_result == 'competitor_wins':
                        competitor_data[competitor]['comparison_wins'] += 1
                
                # Extract context
                if comp_lower in text:
                    context = self._extract_mention_context(text, competitor)
                    competitor_data[competitor]['contexts'].append(context)
        
        return competitor_data
    
    def _analyze_comparison(self, text: str, brand: str, competitor: str) -> str:
        """Determine winner in a comparison"""
        text_lower = text.lower()
        brand_lower = brand.lower()
        comp_lower = competitor.lower()
        
        # Positive indicators for brand
        brand_positive = [
            f"{brand_lower} is better",
            f"{brand_lower} outperforms",
            f"prefer {brand_lower}",
            f"{brand_lower} beats",
            f"choose {brand_lower}",
            f"{brand_lower} superior"
        ]
        
        # Positive indicators for competitor
        comp_positive = [
            f"{comp_lower} is better",
            f"{comp_lower} outperforms",
            f"prefer {comp_lower}",
            f"{comp_lower} beats",
            f"choose {comp_lower}",
            f"{comp_lower} superior"
        ]
        
        brand_score = sum(1 for phrase in brand_positive if phrase in text_lower)
        comp_score = sum(1 for phrase in comp_positive if phrase in text_lower)
        
        if brand_score > comp_score:
            return 'brand_wins'
        elif comp_score > brand_score:
            return 'competitor_wins'
        else:
            return 'neutral'
    
    def _extract_mention_context(self, text: str, competitor: str, context_size: int = 100) -> str:
        """Extract context around competitor mention"""
        comp_lower = competitor.lower()
        text_lower = text.lower()
        
        # Find first occurrence
        pos = text_lower.find(comp_lower)
        if pos == -1:
            return ""
        
        # Extract context
        start = max(0, pos - context_size)
        end = min(len(text), pos + len(competitor) + context_size)
        
        return text[start:end].strip()
    
    def calculate_competitive_landscape(self, competitor_data: Dict) -> Dict:
        """Calculate overall competitive landscape metrics"""
        total_mentions = sum(data['mention_count'] for data in competitor_data.values())
        
        landscape = {
            'total_competitor_mentions': total_mentions,
            'top_competitors': [],
            'competitive_intensity': 0,
            'market_concentration': 0
        }
        
        if total_mentions == 0:
            return landscape
        
        # Calculate market share for each competitor
        market_shares = []
        for comp, data in competitor_data.items():
            share = data['mention_count'] / total_mentions
            market_shares.append({
                'competitor': comp,
                'share': share,
                'mention_count': data['mention_count']
            })
        
        # Sort by share
        market_shares.sort(key=lambda x: x['share'], reverse=True)
        landscape['top_competitors'] = market_shares[:3]
        
        # Calculate competitive intensity (how often competitors are mentioned together)
        co_mention_rate = sum(data['co_mentions'] for data in competitor_data.values()) / len(competitor_data)
        landscape['competitive_intensity'] = min(co_mention_rate / 5, 1)  # Normalize to 0-1
        
        # Calculate market concentration (Herfindahl index)
        hhi = sum(share['share'] ** 2 for share in market_shares)
        landscape['market_concentration'] = hhi
        
        return landscape
    
    def identify_competitive_advantages(self, responses: List[Dict], brand_name: str) -> List[str]:
        """Identify mentioned competitive advantages"""
        advantages = []
        advantage_keywords = {
            'price': ['cheaper', 'affordable', 'value', 'cost-effective', 'budget'],
            'quality': ['quality', 'durable', 'reliable', 'well-made', 'premium'],
            'innovation': ['innovative', 'cutting-edge', 'advanced', 'modern', 'latest'],
            'service': ['service', 'support', 'customer care', 'responsive', 'helpful'],
            'features': ['features', 'functionality', 'capabilities', 'options', 'versatile'],
            'performance': ['performance', 'fast', 'efficient', 'powerful', 'effective'],
            'design': ['design', 'aesthetic', 'beautiful', 'stylish', 'elegant'],
            'reputation': ['trusted', 'reputable', 'established', 'popular', 'recommended']
        }
        
        brand_lower = brand_name.lower()
        
        for response in responses:
            text = response.get('response', '')
            
            # Find sentences mentioning the brand
            sentences = text.split('.')
            for sentence in sentences:
                if brand_lower in sentence.lower():
                    # Check for advantage keywords
                    for category, keywords in advantage_keywords.items():
                        if any(keyword in sentence.lower() for keyword in keywords):
                            advantages.append(category)
        
        # Return unique advantages
        return list(set(advantages))
Nie zatrzymuj się, kontynuuj do kroku 5.

KROK 5: IMPLEMENTACJA API REST
Zadanie: Stwórz kompletne API REST z FastAPI do obsługi systemu.
Plik api/schemas.py:
from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional, Dict
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class User(UserBase):
    id: int
    is_active: bool
    is_admin: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

# Brand schemas
class BrandBase(BaseModel):
    name: str
    description: Optional[str] = None
    industry: Optional[str] = None
    keywords: List[str] = Field(default_factory=list)
    competitors: List[str] = Field(default_factory=list)

class BrandCreate(BrandBase):
    pass

class Brand(BrandBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Query schemas
class QueryCreate(BaseModel):
    brand_id: int
    query_text: str
    query_type: Optional[str] = None
    platform: Optional[str] = None

class Query(BaseModel):
    id: int
    brand_id: int
    query_text: str
    query_type: Optional[str]
    platform: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Response schemas
class AIResponseCreate(BaseModel):
    query_id: int
    platform: str
    response_text: str
    response_metadata: Optional[Dict] = None
    response_time: Optional[float] = None

class AIResponse(BaseModel):
    id: int
    query_id: int
    platform: str
    response_text: str
    response_metadata: Optional[Dict]
    response_time: Optional[float]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Mention schemas
class MentionCreate(BaseModel):
    brand_id: int
    response_id: int
    mention_text: str
    context: str
    sentiment_score: float
    confidence_score: float
    position_in_response: int
    is_primary_focus: bool = False
    competitor_mentions: Optional[List[str]] = None

class Mention(BaseModel):
    id: int
    brand_id: int
    response_id: int
    mention_text: str
    context: str
    sentiment_score: float
    confidence_score: float
    position_in_response: int
    is_primary_focus: bool
    competitor_mentions: Optional[List[str]]
    created_at: datetime
    
    class Config:
        from_attributes = True

# Analytics schemas
class AnalyticsCreate(BaseModel):
    brand_id: int
    visibility_score: float
    sentiment_score: float
    authority_score: float
    share_of_voice: float
    total_mentions: int
    positive_mentions: int
    negative_mentions: int
    neutral_mentions: int
    competitor_comparison: Optional[Dict] = None
    top_keywords: Optional[List[str]] = None
    platform_breakdown: Optional[Dict] = None

class Analytics(AnalyticsCreate):
    id: int
    date: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True

# Scan schemas
class ScanRequest(BaseModel):
    brand_id: int
    platforms: List[str] = Field(default_factory=lambda: ["chatgpt", "claude", "perplexity"])
    num_queries: int = Field(default=10, ge=1, le=50)

class ScanStatus(BaseModel):
    scan_id: int
    brand_id: int
    status: str
    queries_count: int
    responses_count: int
    mentions_count: int
    started_at: datetime
    completed_at: Optional[datetime]
    error_message: Optional[str]

# Report schemas
class ReportRequest(BaseModel):
    brand_id: int
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    include_competitors: bool = True
    format: str = Field(default="json", pattern="^(json|csv|pdf)$")

class ComparisonReport(BaseModel):
    brand: str
    competitors: List[Dict[str, float]]
    visibility_comparison: Dict[str, float]
    sentiment_comparison: Dict[str, float]
    share_of_voice: Dict[str, float]
    period: Dict[str, datetime]
Plik api/auth.py:
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session
from app.database.database import get_db
from app.database.models import User
from app.config import config

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash password"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, config.SECRET_KEY, algorithm=config.ALGORITHM)
    return encoded_jwt

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get user by email"""
    return db.query(User).filter(User.email == email).first()

def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """Authenticate user"""
    user = get_user_by_email(db, email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """Get current user from token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, config.SECRET_KEY, algorithms=[config.ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = get_user_by_email(db, email=email)
    if user is None:
        raise credentials_exception
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

async def get_current_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """Get current admin user"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user
Plik api/routes.py:
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
import json

from app.database.database import get_db
from app.database import models
from app.api import schemas
from app.api.auth import (
    authenticate_user, create_access_token, get_current_active_user,
    get_password_hash, get_current_admin_user
)
from app.scrapers.query_generator import QueryGenerator
from app.analyzers.nlp_analyzer import NLPAnalyzer
from app.analyzers.sentiment import SentimentAnalyzer
from app.analyzers.scoring import ScoringEngine
from app.analyzers.competitor import CompetitorAnalyzer
from app.config import config

router = APIRouter()

# Authentication endpoints
@router.post("/token", response_model=schemas.Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """Login endpoint"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=config.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )
    
    # Update last login
    user.last_login = datetime.utcnow()
    db.commit()
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/register", response_model=schemas.User)
async def register(user: schemas.UserCreate, db: Session = Depends(get_db)):
    """Register new user"""
    # Check if user exists
    db_user = db.query(models.User).filter(models.User.email == user.email).first()
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        email=user.email,
        hashed_password=hashed_password,
        full_name=user.full_name
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

# Brand endpoints
@router.post("/brands", response_model=schemas.Brand)
async def create_brand(
    brand: schemas.BrandCreate,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create new brand"""
    # Check if brand exists
    existing_brand = db.query(models.Brand).filter(models.Brand.name == brand.name).first()
    if existing_brand:
        raise HTTPException(status_code=400, detail="Brand already exists")
    
    # Create brand
    db_brand = models.Brand(
        name=brand.name,
        description=brand.description,
        industry=brand.industry,
        keywords=brand.keywords,
        competitors=brand.competitors
    )
    db.add(db_brand)
    db.commit()
    db.refresh(db_brand)
    
    # Create user-brand association
    user_brand = models.UserBrand(
        user_id=current_user.id,
        brand_id=db_brand.id,
        role='admin'
    )
    db.add(user_brand)
    db.commit()
    
    return db_brand

@router.get("/brands", response_model=List[schemas.Brand])
async def list_brands(
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List user's brands"""
    # Get brands associated with user
    user_brands = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id
    ).all()
    
    brand_ids = [ub.brand_id for ub in user_brands]
    brands = db.query(models.Brand).filter(
        models.Brand.id.in_(brand_ids),
        models.Brand.is_active == True
    ).offset(skip).limit(limit).all()
    
    return brands

@router.get("/brands/{brand_id}", response_model=schemas.Brand)
async def get_brand(
    brand_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get brand details"""
    # Check user has access to brand
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()
    
    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")
    
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")
    
    return brand

# Scanning endpoints
@router.post("/brands/{brand_id}/scan", response_model=schemas.ScanStatus)
async def start_scan(
    brand_id: int,
    scan_request: schemas.ScanRequest,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Start brand scan"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()
    
    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get brand
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")
    
    # Create scan history entry
    scan_history = models.ScanHistory(
        brand_id=brand_id,
        scan_type='manual',
        status='pending',
        queries_count=scan_request.num_queries
    )
    db.add(scan_history)
    db.commit()
    db.refresh(scan_history)
    
    # Start background scan
    background_tasks.add_task(
        run_brand_scan,
        scan_history.id,
        brand,
        scan_request.platforms,
        scan_request.num_queries,
        db
    )
    
    return scan_history

async def run_brand_scan(
    scan_id: int,
    brand: models.Brand,
    platforms: List[str],
    num_queries: int,
    db: Session
):
    """Run brand scan in background"""
    try:
        # Update scan status
        scan = db.query(models.ScanHistory).filter(models.ScanHistory.id == scan_id).first()
        scan.status = 'running'
        scan.started_at = datetime.utcnow()
        db.commit()
        
        # Initialize components
        query_gen = QueryGenerator()
        nlp_analyzer = NLPAnalyzer()
        sentiment_analyzer = SentimentAnalyzer()
        
        # Generate queries
        brand_dict = {
            'id': brand.id,
            'name': brand.name,
            'industry': brand.industry,
            'competitors': brand.competitors,
            'keywords': brand.keywords
        }
        queries = query_gen.generate_queries(brand_dict, num_queries)
        
        # Save queries to database
        db_queries = []
        for query_data in queries:
            db_query = models.Query(
                brand_id=brand.id,
                query_text=query_data['query_text'],
                query_type=query_data['query_type']
            )
            db.add(db_query)
            db_queries.append(db_query)
        db.commit()
        
        # Process each query on each platform
        responses_count = 0
        mentions_count = 0
        
        for platform in platforms:
            # Initialize scraper (simplified for this example)
            # In production, you would initialize the actual scrapers
            
            for db_query in db_queries:
                # Simulate scraping (in production, use actual scrapers)
                response_text = f"Sample response for {db_query.query_text} from {platform}"
                
                # Save response
                db_response = models.AIResponse(
                    query_id=db_query.id,
                    platform=platform,
                    response_text=response_text,
                    response_time=2.5
                )
                db.add(db_response)
                db.commit()
                responses_count += 1
                
                # Analyze response
                mentions = nlp_analyzer.extract_brand_mentions(
                    response_text,
                    brand.name,
                    brand.keywords
                )
                
                # Save mentions
                for mention in mentions:
                    sentiment = sentiment_analyzer.analyze_mention_sentiment(
                        mention['context'],
                        brand.name
                    )
                    
                    db_mention = models.Mention(
                        brand_id=brand.id,
                        response_id=db_response.id,
                        mention_text=mention['matched_text'],
                        context=mention['context'],
                        sentiment_score=sentiment['score'],
                        confidence_score=sentiment['confidence'],
                        position_in_response=mention['position'],
                        is_primary_focus=mention['is_primary']
                    )
                    db.add(db_mention)
                    mentions_count += 1
                
                db.commit()
        
        # Update scan status
        scan.status = 'completed'
        scan.completed_at = datetime.utcnow()
        scan.responses_count = responses_count
        scan.mentions_count = mentions_count
        db.commit()
        
    except Exception as e:
        # Update scan with error
        scan = db.query(models.ScanHistory).filter(models.ScanHistory.id == scan_id).first()
        scan.status = 'failed'
        scan.error_message = str(e)
        scan.completed_at = datetime.utcnow()
        db.commit()

# Analytics endpoints
@router.get("/brands/{brand_id}/analytics", response_model=schemas.Analytics)
async def get_brand_analytics(
    brand_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get brand analytics"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()
    
    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get latest analytics
    analytics = db.query(models.Analytics).filter(
        models.Analytics.brand_id == brand_id
    ).order_by(models.Analytics.created_at.desc()).first()
    
    if not analytics:
        # Generate analytics on the fly
        analytics = generate_brand_analytics(brand_id, db)
    
    return analytics

def generate_brand_analytics(brand_id: int, db: Session) -> models.Analytics:
    """Generate analytics for brand"""
    # Initialize analyzers
    scoring_engine = ScoringEngine()
    competitor_analyzer = CompetitorAnalyzer()
    
    # Get brand data
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    
    # Get recent mentions
    mentions = db.query(models.Mention).filter(
        models.Mention.brand_id == brand_id
    ).all()
    
    # Get total queries
    total_queries = db.query(models.Query).filter(
        models.Query.brand_id == brand_id
    ).count()
    
    # Calculate scores
    visibility_score = scoring_engine.calculate_visibility_score(
        [{'is_primary': m.is_primary_focus, 'sentiment': {'score': m.sentiment_score}} for m in mentions],
        total_queries
    )
    
    sentiment_score = scoring_engine.calculate_sentiment_score(
        [{'sentiment': {'score': m.sentiment_score, 'confidence': m.confidence_score}} for m in mentions]
    )
    
    # Count mention types
    positive_mentions = sum(1 for m in mentions if m.sentiment_score > 0.2)
    negative_mentions = sum(1 for m in mentions if m.sentiment_score < -0.2)
    neutral_mentions = len(mentions) - positive_mentions - negative_mentions
    
    # Create analytics record
    analytics = models.Analytics(
        brand_id=brand_id,
        date=datetime.utcnow(),
        visibility_score=visibility_score,
        sentiment_score=sentiment_score,
        authority_score=50.0,  # Placeholder
        share_of_voice=25.0,  # Placeholder
        total_mentions=len(mentions),
        positive_mentions=positive_mentions,
        negative_mentions=negative_mentions,
        neutral_mentions=neutral_mentions
    )
    
    db.add(analytics)
    db.commit()
    db.refresh(analytics)
    
    return analytics

# Comparison endpoints
@router.get("/brands/{brand_id}/comparison", response_model=schemas.ComparisonReport)
async def get_comparison_report(
    brand_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get brand comparison with competitors"""
    # Verify access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == brand_id
    ).first()
    
    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get brand
    brand = db.query(models.Brand).filter(models.Brand.id == brand_id).first()
    if not brand:
        raise HTTPException(status_code=404, detail="Brand not found")
    
    # Generate comparison report
    report = {
        'brand': brand.name,
        'competitors': [],
        'visibility_comparison': {brand.name: 75.0},  # Placeholder
        'sentiment_comparison': {brand.name: 60.0},   # Placeholder
        'share_of_voice': {brand.name: 35.0},        # Placeholder
        'period': {
            'start': datetime.utcnow() - timedelta(days=30),
            'end': datetime.utcnow()
        }
    }
    
    # Add competitor data (placeholder)
    for competitor in brand.competitors[:3]:
        report['competitors'].append({'name': competitor, 'score': 50.0})
        report['visibility_comparison'][competitor] = 65.0
        report['sentiment_comparison'][competitor] = 55.0
        report['share_of_voice'][competitor] = 20


### References

1. **How to Build an AI Chatbot in 2025: Step-by-Step Guide**. [https://botpress.com](https://botpress.com/blog/how-to-build-your-own-ai-chatbot)
2. **Jak wdrożyć live chat w sklepie internetowym i zwiększyć sprzedaż? - widoczni**. [https://widoczni.com](https://widoczni.com/blog/live-chat-e-commerce/)
3. **Jak pisać skuteczne prompty AI w ChatGPT? 90 przykładów! - widoczni**. [https://widoczni.com](https://widoczni.com/blog/najlepsze-prompty-chatgpt/)
4. **Jak stworzyć chatbota dla Twojej strony internetowej**. [https://www.groupon.pl](https://www.groupon.pl/merchant/zarzadzanie-firma/narzedzia-internetowe/tworzenie-chatbota-dla-twojej-strony-internetowej)
5. **ChatGPT – Przewodnik po AI i jego zastosowaniach w codziennym życiu**. [https://webwavecms.com](https://webwavecms.com/blog/chat-gpt)
6. **Co to jest Chatbot? Projektowanie chatbota krok po kroku**. [https://thestory.is](https://thestory.is/pl/journal/co-to-chatbot-projektowanie/)
7. **25 narzędzi do podglądania konkurencji w sieci / Agencja e-marketingowa i e-commerce Ideo Force**. [https://www.ideoforce.pl](https://www.ideoforce.pl/akademia/25-narzedzi-do-podgladania-konkurencji-w-sieci,14.html)
8. **7 sposobów, jak wykorzystać AI i ChatGPT w e-commerce**. [https://ageno.pl](https://ageno.pl/blog/chatgpt-w-e-commerce/)
9. **How to avoid these 5 common mistakes when creating a chatbot - SentiOne Blog**. [https://sentione.com](https://sentione.com/blog/how-to-avoid-these-5-common-mistakes-when-creating-a-chatbot)
10. **7 life hacków do pracy z ChatGPT, o których mogłeś nie wiedzieć**. [https://blog.ringostat.com](https://blog.ringostat.com/pl/7-life-hackow-do-pracy-z-chatgpt-o-ktorych-mogles-nie-wiedziec/)
Hubert Gerasik
<EMAIL>
June 16, 2025
Kontynuuj
Plik api/routes.py (kontynuacja):
    return report

# Mentions endpoints
@router.get("/mentions", response_model=List[schemas.Mention])
async def list_mentions(
    brand_id: Optional[int] = None,
    sentiment: Optional[str] = None,
    limit: int = 50,
    skip: int = 0,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List mentions with filtering"""
    # Get user's brands
    user_brands = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id
    ).all()
    allowed_brand_ids = [ub.brand_id for ub in user_brands]
    
    # Build query
    query = db.query(models.Mention)
    
    if brand_id:
        if brand_id not in allowed_brand_ids:
            raise HTTPException(status_code=403, detail="Access denied")
        query = query.filter(models.Mention.brand_id == brand_id)
    else:
        query = query.filter(models.Mention.brand_id.in_(allowed_brand_ids))
    
    # Filter by sentiment
    if sentiment:
        if sentiment == "positive":
            query = query.filter(models.Mention.sentiment_score > 0.2)
        elif sentiment == "negative":
            query = query.filter(models.Mention.sentiment_score < -0.2)
        elif sentiment == "neutral":
            query = query.filter(
                models.Mention.sentiment_score >= -0.2,
                models.Mention.sentiment_score <= 0.2
            )
    
    # Order by date and paginate
    mentions = query.order_by(models.Mention.created_at.desc()).offset(skip).limit(limit).all()
    
    return mentions

# Alert endpoints
@router.get("/alerts")
async def list_alerts(
    unread_only: bool = False,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """List user's alerts"""
    # Get user's brands
    user_brands = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id
    ).all()
    allowed_brand_ids = [ub.brand_id for ub in user_brands]
    
    # Query alerts
    query = db.query(models.Alert).filter(
        models.Alert.brand_id.in_(allowed_brand_ids)
    )
    
    if unread_only:
        query = query.filter(models.Alert.is_read == False)
    
    alerts = query.order_by(models.Alert.created_at.desc()).limit(50).all()
    
    return alerts

@router.put("/alerts/{alert_id}/read")
async def mark_alert_read(
    alert_id: int,
    current_user: models.User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Mark alert as read"""
    alert = db.query(models.Alert).filter(models.Alert.id == alert_id).first()
    if not alert:
        raise HTTPException(status_code=404, detail="Alert not found")
    
    # Verify user has access
    user_brand = db.query(models.UserBrand).filter(
        models.UserBrand.user_id == current_user.id,
        models.UserBrand.brand_id == alert.brand_id
    ).first()
    
    if not user_brand:
        raise HTTPException(status_code=403, detail="Access denied")
    
    alert.is_read = True
    db.commit()
    
    return {"status": "success"}
Nie zatrzymuj się, kontynuuj do kroku 6.

KROK 6: IMPLEMENTACJA SCHEDULERA I AUTOMATYZACJI
Zadanie: Stwórz system automatycznego skanowania i generowania raportów.
Plik scheduler.py:
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from datetime import datetime, timedelta
import asyncio
import logging
from typing import List, Dict

from app.database.database import get_db_session
from app.database import models
from app.scrapers.query_generator import QueryGenerator
from app.scrapers.chatgpt_scraper import ChatGPTScraper
from app.scrapers.claude_scraper import ClaudeScraper
from app.scrapers.perplexity_scraper import PerplexityScraper
from app.analyzers.nlp_analyzer import NLPAnalyzer
from app.analyzers.sentiment import SentimentAnalyzer
from app.analyzers.scoring import ScoringEngine
from app.analyzers.competitor import CompetitorAnalyzer
from app.notifications.email_sender import EmailSender
from app.config import config

logger = logging.getLogger(__name__)

class BrandMonitorScheduler:
    """Scheduler for automated brand monitoring tasks"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.query_generator = QueryGenerator()
        self.nlp_analyzer = NLPAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.scoring_engine = ScoringEngine()
        self.competitor_analyzer = CompetitorAnalyzer()
        self.email_sender = EmailSender()
        
        # Initialize scrapers
        self.scrapers = {
            'chatgpt': ChatGPTScraper(headless=True),
            'claude': ClaudeScraper(headless=True),
            'perplexity': PerplexityScraper(headless=True)
        }
    
    def start(self):
        """Start the scheduler"""
        # Schedule daily brand scanning
        self.scheduler.add_job(
            self.daily_brand_scan,
            CronTrigger.from_crontab(config.SCAN_SCHEDULE),
            id='daily_scan',
            replace_existing=True
        )
        
        # Schedule weekly reports
        self.scheduler.add_job(
            self.weekly_report_generation,
            CronTrigger.from_crontab(config.REPORT_SCHEDULE),
            id='weekly_report',
            replace_existing=True
        )
        
        # Schedule hourly alert check
        self.scheduler.add_job(
            self.check_alerts,
            'interval',
            hours=1,
            id='alert_check',
            replace_existing=True
        )
        
        # Schedule daily cleanup
        self.scheduler.add_job(
            self.cleanup_old_data,
            'cron',
            hour=3,
            minute=0,
            id='daily_cleanup',
            replace_existing=True
        )
        
        self.scheduler.start()
        logger.info("Scheduler started successfully")
    
    def stop(self):
        """Stop the scheduler"""
        self.scheduler.shutdown()
        logger.info("Scheduler stopped")
    
    async def daily_brand_scan(self):
        """Perform daily scan for all active brands"""
        logger.info("Starting daily brand scan")
        
        with get_db_session() as db:
            # Get all active brands
            active_brands = db.query(models.Brand).filter(
                models.Brand.is_active == True
            ).all()
            
            for brand in active_brands:
                try:
                    await self._scan_brand(brand, db)
                except Exception as e:
                    logger.error(f"Error scanning brand {brand.name}: {str(e)}")
                    
                    # Create alert for scan failure
                    alert = models.Alert(
                        brand_id=brand.id,
                        alert_type='scan_failure',
                        severity='high',
                        title=f"Daily scan failed for {brand.name}",
                        message=f"Error: {str(e)}"
                    )
                    db.add(alert)
                    db.commit()
    
    async def _scan_brand(self, brand: models.Brand, db):
        """Scan a single brand"""
        logger.info(f"Scanning brand: {brand.name}")
        
        # Create scan history
        scan_history = models.ScanHistory(
            brand_id=brand.id,
            scan_type='scheduled',
            status='running',
            started_at=datetime.utcnow()
        )
        db.add(scan_history)
        db.commit()
        
        try:
            # Generate queries
            brand_dict = {
                'id': brand.id,
                'name': brand.name,
                'industry': brand.industry,
                'competitors': brand.competitors,
                'keywords': brand.keywords
            }
            queries = self.query_generator.generate_queries(brand_dict, num_queries=5)
            
            # Add competitor comparison queries
            for competitor in brand.competitors[:2]:  # Limit to top 2 competitors
                comp_queries = self.query_generator.generate_competitor_queries(brand_dict, competitor)
                queries.extend(comp_queries[:2])  # Add 2 queries per competitor
            
            responses_count = 0
            mentions_count = 0
            
            # Process each query
            for query_data in queries:
                # Save query
                db_query = models.Query(
                    brand_id=brand.id,
                    query_text=query_data['query_text'],
                    query_type=query_data['query_type']
                )
                db.add(db_query)
                db.commit()
                
                # Scrape from each platform
                for platform_name, scraper in self.scrapers.items():
                    if not config.PLATFORMS.get(platform_name, {}).get('enabled', False):
                        continue
                    
                    try:
                        # Get response
                        result = scraper.scrape_response(query_data['query_text'])
                        
                        if result['success'] and result['response']:
                            # Save response
                            db_response = models.AIResponse(
                                query_id=db_query.id,
                                platform=platform_name,
                                response_text=result['response'],
                                response_time=result['response_time'],
                                response_metadata={'timestamp': result['timestamp']}
                            )
                            db.add(db_response)
                            db.commit()
                            responses_count += 1
                            
                            # Analyze response
                            mentions = self.nlp_analyzer.extract_brand_mentions(
                                result['response'],
                                brand.name,
                                brand.keywords
                            )
                            
                            # Save mentions
                            for mention in mentions:
                                sentiment = self.sentiment_analyzer.analyze_mention_sentiment(
                                    mention['context'],
                                    brand.name
                                )
                                
                                db_mention = models.Mention(
                                    brand_id=brand.id,
                                    response_id=db_response.id,
                                    mention_text=mention['matched_text'],
                                    context=mention['context'],
                                    sentiment_score=sentiment['score'],
                                    confidence_score=sentiment['confidence'],
                                    position_in_response=mention['position'],
                                    is_primary_focus=mention['is_primary']
                                )
                                db.add(db_mention)
                                mentions_count += 1
                            
                            db.commit()
                            
                    except Exception as e:
                        logger.error(f"Error scraping {platform_name}: {str(e)}")
                
                # Add delay between queries
                await asyncio.sleep(config.SCRAPING_DELAY)
            
            # Update scan history
            scan_history.status = 'completed'
            scan_history.completed_at = datetime.utcnow()
            scan_history.queries_count = len(queries)
            scan_history.responses_count = responses_count
            scan_history.mentions_count = mentions_count
            db.commit()
            
            # Generate analytics
            await self._generate_analytics(brand, db)
            
        except Exception as e:
            scan_history.status = 'failed'
            scan_history.error_message = str(e)
            scan_history.completed_at = datetime.utcnow()
            db.commit()
            raise
    
    async def _generate_analytics(self, brand: models.Brand, db):
        """Generate analytics for brand"""
        logger.info(f"Generating analytics for {brand.name}")
        
        # Get recent mentions (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_mentions = db.query(models.Mention).filter(
            models.Mention.brand_id == brand.id,
            models.Mention.created_at >= yesterday
        ).all()
        
        # Get all responses for authority score
        recent_responses = db.query(models.AIResponse).join(
            models.Query
        ).filter(
            models.Query.brand_id == brand.id,
            models.AIResponse.created_at >= yesterday
        ).all()
        
        # Get total queries
        total_queries = db.query(models.Query).filter(
            models.Query.brand_id == brand.id,
            models.Query.created_at >= yesterday
        ).count()
        
        # Calculate scores
        mention_data = [{
            'is_primary': m.is_primary_focus,
            'sentiment': {
                'score': m.sentiment_score,
                'confidence': m.confidence_score
            }
        } for m in recent_mentions]
        
        visibility_score = self.scoring_engine.calculate_visibility_score(mention_data, total_queries)
        sentiment_score = self.scoring_engine.calculate_sentiment_score(mention_data)
        
        response_data = [{'response': r.response_text} for r in recent_responses]
        authority_score = self.scoring_engine.calculate_authority_score(mention_data, response_data)
        
        # Calculate competitor metrics
        competitor_mentions = {}
        for competitor in brand.competitors:
            comp_count = 0
            for response in recent_responses:
                if competitor.lower() in response.response_text.lower():
                    comp_count += 1
            competitor_mentions[competitor] = comp_count
        
        share_of_voice = self.scoring_engine.calculate_share_of_voice(
            len(recent_mentions),
            competitor_mentions
        )
        
        # Count mention types
        positive_mentions = sum(1 for m in recent_mentions if m.sentiment_score > 0.2)
        negative_mentions = sum(1 for m in recent_mentions if m.sentiment_score < -0.2)
        neutral_mentions = len(recent_mentions) - positive_mentions - negative_mentions
        
        # Extract top keywords
        all_text = ' '.join([r.response_text for r in recent_responses])
        top_keywords = self.nlp_analyzer.extract_keywords(all_text, num_keywords=10)
        
        # Create analytics record
        analytics = models.Analytics(
            brand_id=brand.id,
            date=datetime.utcnow(),
            visibility_score=visibility_score,
            sentiment_score=sentiment_score,
            authority_score=authority_score,
            share_of_voice=share_of_voice,
            total_mentions=len(recent_mentions),
            positive_mentions=positive_mentions,
            negative_mentions=negative_mentions,
            neutral_mentions=neutral_mentions,
            competitor_comparison=competitor_mentions,
            top_keywords=[kw[0] for kw in top_keywords],
            platform_breakdown={
                'chatgpt': sum(1 for r in recent_responses if r.platform == 'chatgpt'),
                'claude': sum(1 for r in recent_responses if r.platform == 'claude'),
                'perplexity': sum(1 for r in recent_responses if r.platform == 'perplexity')
            }
        )
        
        db.add(analytics)
        db.commit()
        
        # Check for alerts
        await self._check_brand_alerts(brand, analytics, db)
    
    async def _check_brand_alerts(self, brand: models.Brand, analytics: models.Analytics, db):
        """Check if any alerts should be triggered"""
        # Get previous analytics
        previous_analytics = db.query(models.Analytics).filter(
            models.Analytics.brand_id == brand.id,
            models.Analytics.id != analytics.id
        ).order_by(models.Analytics.created_at.desc()).first()
        
        if not previous_analytics:
            return
        
        # Check visibility drop
        if analytics.visibility_score < previous_analytics.visibility_score * 0.8:
            alert = models.Alert(
                brand_id=brand.id,
                alert_type='visibility_drop',
                severity='high',
                title=f"Visibility drop detected for {brand.name}",
                message=f"Visibility score dropped from {previous_analytics.visibility_score:.1f} to {analytics.visibility_score:.1f}",
                data={
                    'previous_score': previous_analytics.visibility_score,
                    'current_score': analytics.visibility_score,
                    'drop_percentage': ((previous_analytics.visibility_score - analytics.visibility_score) / previous_analytics.visibility_score) * 100
                }
            )
            db.add(alert)
        
        # Check sentiment drop
        if analytics.sentiment_score < -50 and analytics.sentiment_score < previous_analytics.sentiment_score - 20:
            alert = models.Alert(
                brand_id=brand.id,
                alert_type='negative_sentiment',
                severity='critical',
                title=f"Negative sentiment surge for {brand.name}",
                message=f"Sentiment score dropped to {analytics.sentiment_score:.1f}",
                data={
                    'sentiment_score': analytics.sentiment_score,
                    'negative_mentions': analytics.negative_mentions
                }
            )
            db.add(alert)
        
        # Check competitor surge
        if previous_analytics.share_of_voice > 30 and analytics.share_of_voice < 20:
            alert = models.Alert(
                brand_id=brand.id,
                alert_type='competitor_surge',
                severity='medium',
                title=f"Competitors gaining share of voice",
                message=f"Your share of voice dropped from {previous_analytics.share_of_voice:.1f}% to {analytics.share_of_voice:.1f}%",
                data={
                    'previous_share': previous_analytics.share_of_voice,
                    'current_share': analytics.share_of_voice
                }
            )
            db.add(alert)
        
        db.commit()
    
    async def weekly_report_generation(self):
        """Generate weekly reports for all brands"""
        logger.info("Starting weekly report generation")
        
        with get_db_session() as db:
            # Get all active brands
            active_brands = db.query(models.Brand).filter(
                models.Brand.is_active == True
            ).all()
            
            for brand in active_brands:
                try:
                    await self._generate_weekly_report(brand, db)
                except Exception as e:
                    logger.error(f"Error generating report for {brand.name}: {str(e)}")
    
    async def _generate_weekly_report(self, brand: models.Brand, db):
        """Generate weekly report for a brand"""
        logger.info(f"Generating weekly report for {brand.name}")
        
        # Get analytics for the past week
        week_ago = datetime.utcnow() - timedelta(days=7)
        analytics = db.query(models.Analytics).filter(
            models.Analytics.brand_id == brand.id,
            models.Analytics.created_at >= week_ago
        ).order_by(models.Analytics.created_at).all()
        
        if not analytics:
            logger.warning(f"No analytics data for {brand.name}")
            return
        
        # Calculate weekly metrics
        avg_visibility = sum(a.visibility_score for a in analytics) / len(analytics)
        avg_sentiment = sum(a.sentiment_score for a in analytics) / len(analytics)
        total_mentions = sum(a.total_mentions for a in analytics)
        
        # Get trend
        trend = self.scoring_engine.calculate_trend_score([{
            'date': a.created_at,
            'visibility_score': a.visibility_score
        } for a in analytics])
        
        # Get top performing queries
        top_queries = db.query(
            models.Query.query_text,
            models.Query.query_type
        ).join(
            models.AIResponse
        ).join(
            models.Mention
        ).filter(
            models.Query.brand_id == brand.id,
            models.Query.created_at >= week_ago,
            models.Mention.is_primary_focus == True
        ).group_by(
            models.Query.query_text,
            models.Query.query_type
        ).limit(5).all()
        
        # Prepare report data
        report_data = {
            'brand': brand.name,
            'period': {
                'start': week_ago,
                'end': datetime.utcnow()
            },
            'metrics': {
                'avg_visibility': avg_visibility,
                'avg_sentiment': avg_sentiment,
                'total_mentions': total_mentions,
                'trend': trend
            },
            'top_queries': [{'query': q[0], 'type': q[1]} for q in top_queries],
            'daily_scores': [{
                'date': a.created_at.strftime('%Y-%m-%d'),
                'visibility': a.visibility_score,
                'sentiment': a.sentiment_score,
                'mentions': a.total_mentions
            } for a in analytics]
        }
        
        # Send email report
        users = db.query(models.User).join(
            models.UserBrand
        ).filter(
            models.UserBrand.brand_id == brand.id,
            models.UserBrand.role.in_(['admin', 'editor'])
        ).all()
        
        for user in users:
            await self.email_sender.send_weekly_report(user.email, brand.name, report_data)
    
    async def check_alerts(self):
        """Check for alerts that need to be sent"""
        logger.info("Checking for pending alerts")
        
        with get_db_session() as db:
            # Get unread critical alerts
            critical_alerts = db.query(models.Alert).filter(
                models.Alert.is_read == False,
                models.Alert.severity.in_(['critical', 'high']),
                models.Alert.created_at >= datetime.utcnow() - timedelta(hours=1)
            ).all()
            
            for alert in critical_alerts:
                # Get brand
                brand = db.query(models.Brand).filter(
                    models.Brand.id == alert.brand_id
                ).first()
                
                # Get users to notify
                users = db.query(models.User).join(
                    models.UserBrand
                ).filter(
                    models.UserBrand.brand_id == alert.brand_id,
                    models.UserBrand.role.in_(['admin', 'editor'])
                ).all()
                
                # Send notifications
                for user in users:
                    await self.email_sender.send_alert(
                        user.email,
                        brand.name,
                        alert.title,
                        alert.message,
                        alert.severity
                    )
    
    async def cleanup_old_data(self):
        """Clean up old data to manage database size"""
        logger.info("Starting data cleanup")
        
        with get_db_session() as db:
            # Define retention periods
            response_retention_days = 90
            analytics_retention_days = 365
            
            # Delete old AI responses
            cutoff_date = datetime.utcnow() - timedelta(days=response_retention_days)
            old_responses = db.query(models.AIResponse).filter(
                models.AIResponse.created_at < cutoff_date
            ).count()
            
            if old_responses > 0:
                db.query(models.AIResponse).filter(
                    models.AIResponse.created_at < cutoff_date
                ).delete()
                logger.info(f"Deleted {old_responses} old AI responses")
            
            # Delete old analytics (keep monthly summaries)
            analytics_cutoff = datetime.utcnow() - timedelta(days=analytics_retention_days)
            
            # Keep one record per month
            old_analytics = db.query(models.Analytics).filter(
                models.Analytics.created_at < analytics_cutoff
            ).all()
            
            monthly_keepers = {}
            for analytics in old_analytics:
                month_key = analytics.created_at.strftime('%Y-%m')
                brand_month_key = f"{analytics.brand_id}-{month_key}"
                
                if brand_month_key not in monthly_keepers:
                    monthly_keepers[brand_month_key] = analytics
                else:
                    # Delete if not the monthly keeper
                    db.delete(analytics)
            
            db.commit()
            logger.info("Data cleanup completed")

# Global scheduler instance
scheduler = BrandMonitorScheduler()
Nie zatrzymuj się, kontynuuj do kroku 7.

KROK 7: IMPLEMENTACJA DASHBOARD WEBOWY
Zadanie: Stwórz interaktywny dashboard używając Streamlit.
Plik web/dashboard.py:
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime, timedelta
import requests
import json
from typing import Dict, List

# Page config
st.set_page_config(
    page_title="AI Brand Monitor Dashboard",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .alert-box {
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }
    .alert-critical {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
    }
    .alert-high {
        background-color: #fff3e0;
        border-left: 4px solid #ff9800;
    }
    .alert-medium {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
</style>
""", unsafe_allow_html=True)

class BrandMonitorDashboard:
    def __init__(self):
        self.api_base_url = "http://localhost:8000"
        self.init_session_state()
    
    def init_session_state(self):
        """Initialize session state variables"""
        if 'authenticated' not in st.session_state:
            st.session_state.authenticated = False
        if 'token' not in st.session_state:
            st.session_state.token = None
        if 'selected_brand' not in st.session_state:
            st.session_state.selected_brand = None
    
    def make_api_request(self, endpoint: str, method: str = "GET", data: Dict = None):
        """Make authenticated API request"""
        headers = {}
        if st.session_state.token:
            headers['Authorization'] = f"Bearer {st.session_state.token}"
        
        url = f"{self.api_base_url}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 401:
            st.session_state.authenticated = False
            st.error("Session expired. Please login again.")
            return None
        else:
            st.error(f"API Error: {response.status_code}")
            return None
    
    def login_page(self):
        """Display login page"""
        st.markdown('<h1 class="main-header">AI Brand Monitor</h1>', unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns([1, 2, 1])
        
        with col2:
            st.markdown("### Login")
            
            with st.form("login_form"):
                email = st.text_input("Email", placeholder="<EMAIL>")
                password = st.text_input("Password", type="password", placeholder="admin123")
                submit = st.form_submit_button("Login", use_container_width=True)
                
                if submit:
                    # Authenticate
                    response = requests.post(
                        f"{self.api_base_url}/token",
                        data={"username": email, "password": password}
                    )
                    
                    if response.status_code == 200:
                        token_data = response.json()
                        st.session_state.token = token_data['access_token']
                        st.session_state.authenticated = True
                        st.success("Login successful!")
                        st.rerun()
                    else:
                        st.error("Invalid credentials")
    
    def main_dashboard(self):
        """Display main dashboard"""
        # Sidebar
        with st.sidebar:
            st.markdown("## AI Brand Monitor")
            
            # Brand selector
            brands = self.make_api_request("/brands")
            if brands:
                brand_names = [b['name'] for b in brands]
                selected_brand_name = st.selectbox(
                    "Select Brand",
                    brand_names,
                    index=0 if not st.session_state.selected_brand else brand_names.index(st.session_state.selected_brand['name'])
                )
                
                # Update selected brand
                for brand in brands:
                    if brand['name'] == selected_brand_name:
                        st.session_state.selected_brand = brand
                        break
            
            st.divider()
            
            # Actions
            if st.button("🔄 Run Scan", use_container_width=True):
                self.run_scan()
            
            if st.button("📊 Generate Report", use_container_width=True):
                self.generate_report()
            
            st.divider()
            
            # User menu
            if st.button("🚪 Logout", use_container_width=True):
                st.session_state.authenticated = False
                st.session_state.token = None
                st.rerun()
        
        # Main content
        if st.session_state.selected_brand:
            self.display_brand_dashboard()
        else:
            st.info("Please select a brand from the sidebar")
    
    def display_brand_dashboard(self):
        """Display dashboard for selected brand"""
        brand = st.session_state.selected_brand
        
        # Header
        st.markdown(f"# {brand['name']} Brand Monitor")
        st.markdown(f"*{brand.get('description', 'No description')}*")
        
        # Get analytics
        analytics = self.make_api_request(f"/brands/{brand['id']}/analytics")
        
        if analytics:
            # Metrics row
            col1, col2, col3, col4 = st.columns(4


### References

1. **How to Build an AI Chatbot in 2025: Step-by-Step Guide**. [https://botpress.com](https://botpress.com/blog/how-to-build-your-own-ai-chatbot)
2. **Jak wdrożyć live chat w sklepie internetowym i zwiększyć sprzedaż? - widoczni**. [https://widoczni.com](https://widoczni.com/blog/live-chat-e-commerce/)
3. **Jak pisać skuteczne prompty AI w ChatGPT? 90 przykładów! - widoczni**. [https://widoczni.com](https://widoczni.com/blog/najlepsze-prompty-chatgpt/)
4. **Jak stworzyć chatbota dla Twojej strony internetowej**. [https://www.groupon.pl](https://www.groupon.pl/merchant/zarzadzanie-firma/narzedzia-internetowe/tworzenie-chatbota-dla-twojej-strony-internetowej)
5. **Co to jest Chatbot? Projektowanie chatbota krok po kroku**. [https://thestory.is](https://thestory.is/pl/journal/co-to-chatbot-projektowanie/)
6. **ChatGPT – Przewodnik po AI i jego zastosowaniach w codziennym życiu**. [https://webwavecms.com](https://webwavecms.com/blog/chat-gpt)
7. **25 narzędzi do podglądania konkurencji w sieci / Agencja e-marketingowa i e-commerce Ideo Force**. [https://www.ideoforce.pl](https://www.ideoforce.pl/akademia/25-narzedzi-do-podgladania-konkurencji-w-sieci,14.html)
8. **7 sposobów, jak wykorzystać AI i ChatGPT w e-commerce**. [https://ageno.pl](https://ageno.pl/blog/chatgpt-w-e-commerce/)
9. **How to avoid these 5 common mistakes when creating a chatbot - SentiOne Blog**. [https://sentione.com](https://sentione.com/blog/how-to-avoid-these-5-common-mistakes-when-creating-a-chatbot)
10. **7 life hacków do pracy z ChatGPT, o których mogłeś nie wiedzieć**. [https://blog.ringostat.com](https://blog.ringostat.com/pl/7-life-hackow-do-pracy-z-chatgpt-o-ktorych-mogles-nie-wiedziec/)
Hubert Gerasik
<EMAIL>
June 16, 2025
Kontynuuj
Plik web/dashboard.py (kontynuacja):
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric(
                    "Visibility Score",
                    f"{analytics['visibility_score']:.1f}",
                    delta=f"{analytics.get('visibility_change', 0):.1f}%",
                    help="How visible your brand is in AI responses (0-100)"
                )
            
            with col2:
                sentiment_color = "🟢" if analytics['sentiment_score'] > 20 else "🔴" if analytics['sentiment_score'] < -20 else "🟡"
                st.metric(
                    f"{sentiment_color} Sentiment Score",
                    f"{analytics['sentiment_score']:.1f}",
                    delta=f"{analytics.get('sentiment_change', 0):.1f}",
                    help="Overall sentiment towards your brand (-100 to 100)"
                )
            
            with col3:
                st.metric(
                    "Authority Score",
                    f"{analytics['authority_score']:.1f}",
                    help="How authoritative AI considers your brand (0-100)"
                )
            
            with col4:
                st.metric(
                    "Share of Voice",
                    f"{analytics['share_of_voice']:.1f}%",
                    delta=f"{analytics.get('sov_change', 0):.1f}%",
                    help="Your brand's share of mentions vs competitors"
                )
            
            # Tabs for different views
            tab1, tab2, tab3, tab4, tab5 = st.tabs(["📈 Trends", "💬 Mentions", "🏆 Competitors", "🔔 Alerts", "📊 Reports"])
            
            with tab1:
                self.display_trends_tab(brand)
            
            with tab2:
                self.display_mentions_tab(brand)
            
            with tab3:
                self.display_competitors_tab(brand, analytics)
            
            with tab4:
                self.display_alerts_tab(brand)
            
            with tab5:
                self.display_reports_tab(brand)
    
    def display_trends_tab(self, brand: Dict):
        """Display trends visualization"""
        st.subheader("📈 Performance Trends")
        
        # Date range selector
        col1, col2 = st.columns([3, 1])
        with col1:
            date_range = st.date_input(
                "Select Date Range",
                value=(datetime.now() - timedelta(days=30), datetime.now()),
                max_value=datetime.now()
            )
        
        # Fetch historical analytics
        # In production, you'd add date filtering to the API
        analytics_history = []
        
        # Simulate historical data for demo
        for i in range(30):
            date = datetime.now() - timedelta(days=i)
            analytics_history.append({
                'date': date,
                'visibility_score': 75 + (i % 10) - 5,
                'sentiment_score': 60 + (i % 15) - 7,
                'total_mentions': 20 + (i % 8)
            })
        
        df = pd.DataFrame(analytics_history)
        
        # Visibility trend
        fig_visibility = go.Figure()
        fig_visibility.add_trace(go.Scatter(
            x=df['date'],
            y=df['visibility_score'],
            mode='lines+markers',
            name='Visibility Score',
            line=dict(color='#1f77b4', width=3),
            marker=dict(size=8)
        ))
        fig_visibility.update_layout(
            title="Visibility Score Trend",
            xaxis_title="Date",
            yaxis_title="Score",
            height=400,
            hovermode='x unified'
        )
        st.plotly_chart(fig_visibility, use_container_width=True)
        
        # Sentiment and mentions
        col1, col2 = st.columns(2)
        
        with col1:
            fig_sentiment = go.Figure()
            fig_sentiment.add_trace(go.Scatter(
                x=df['date'],
                y=df['sentiment_score'],
                mode='lines+markers',
                name='Sentiment Score',
                line=dict(color='#2ca02c', width=3),
                fill='tozeroy'
            ))
            fig_sentiment.update_layout(
                title="Sentiment Score Trend",
                xaxis_title="Date",
                yaxis_title="Score",
                height=350
            )
            st.plotly_chart(fig_sentiment, use_container_width=True)
        
        with col2:
            fig_mentions = go.Figure()
            fig_mentions.add_trace(go.Bar(
                x=df['date'],
                y=df['total_mentions'],
                name='Total Mentions',
                marker_color='#ff7f0e'
            ))
            fig_mentions.update_layout(
                title="Daily Mentions Count",
                xaxis_title="Date",
                yaxis_title="Mentions",
                height=350
            )
            st.plotly_chart(fig_mentions, use_container_width=True)
    
    def display_mentions_tab(self, brand: Dict):
        """Display recent mentions"""
        st.subheader("💬 Recent Mentions")
        
        # Filters
        col1, col2, col3 = st.columns(3)
        with col1:
            sentiment_filter = st.selectbox(
                "Filter by Sentiment",
                ["All", "Positive", "Negative", "Neutral"]
            )
        with col2:
            platform_filter = st.selectbox(
                "Filter by Platform",
                ["All", "ChatGPT", "Claude", "Perplexity"]
            )
        
        # Fetch mentions
        mentions = self.make_api_request(f"/mentions?brand_id={brand['id']}&limit=20")
        
        if mentions:
            for mention in mentions:
                # Determine sentiment emoji
                sentiment = mention['sentiment_score']
                if sentiment > 0.2:
                    emoji = "😊"
                    color = "green"
                elif sentiment < -0.2:
                    emoji = "😟"
                    color = "red"
                else:
                    emoji = "😐"
                    color = "gray"
                
                # Display mention card
                with st.container():
                    col1, col2 = st.columns([1, 5])
                    with col1:
                        st.markdown(f"<h1 style='text-align: center; color: {color};'>{emoji}</h1>", unsafe_allow_html=True)
                    with col2:
                        st.markdown(f"**Context:** {mention['context']}")
                        st.caption(f"Sentiment: {sentiment:.2f} | Confidence: {mention['confidence_score']:.2f} | {mention['created_at']}")
                    st.divider()
    
    def display_competitors_tab(self, brand: Dict, analytics: Dict):
        """Display competitor analysis"""
        st.subheader("🏆 Competitor Analysis")
        
        # Get comparison data
        comparison = self.make_api_request(f"/brands/{brand['id']}/comparison")
        
        if comparison:
            # Share of Voice pie chart
            col1, col2 = st.columns(2)
            
            with col1:
                sov_data = comparison['share_of_voice']
                fig_sov = go.Figure(data=[go.Pie(
                    labels=list(sov_data.keys()),
                    values=list(sov_data.values()),
                    hole=.3
                )])
                fig_sov.update_layout(
                    title="Share of Voice Distribution",
                    height=400
                )
                st.plotly_chart(fig_sov, use_container_width=True)
            
            with col2:
                # Competitor comparison bar chart
                metrics = ['visibility', 'sentiment']
                competitors = list(comparison['visibility_comparison'].keys())
                
                fig_comp = go.Figure()
                
                for metric in metrics:
                    if metric == 'visibility':
                        values = [comparison['visibility_comparison'][c] for c in competitors]
                        fig_comp.add_trace(go.Bar(name='Visibility', x=competitors, y=values))
                    else:
                        values = [comparison['sentiment_comparison'][c] for c in competitors]
                        fig_comp.add_trace(go.Bar(name='Sentiment', x=competitors, y=values))
                
                fig_comp.update_layout(
                    title="Competitor Comparison",
                    barmode='group',
                    height=400,
                    xaxis_title="Brand",
                    yaxis_title="Score"
                )
                st.plotly_chart(fig_comp, use_container_width=True)
            
            # Competitive advantages
            st.markdown("### Competitive Advantages Mentioned")
            advantages = ["Quality", "Innovation", "Price", "Service", "Features"]
            cols = st.columns(len(advantages))
            
            for i, advantage in enumerate(advantages):
                with cols[i]:
                    # Simulate advantage score
                    score = 70 + (i * 5)
                    st.metric(advantage, f"{score}%")
    
    def display_alerts_tab(self, brand: Dict):
        """Display alerts"""
        st.subheader("🔔 Alerts & Notifications")
        
        # Fetch alerts
        alerts = self.make_api_request("/alerts?unread_only=false")
        
        if alerts:
            # Filter alerts for current brand
            brand_alerts = [a for a in alerts if a['brand_id'] == brand['id']]
            
            if brand_alerts:
                for alert in brand_alerts[:10]:  # Show latest 10
                    severity_class = f"alert-{alert['severity']}"
                    icon = "🔴" if alert['severity'] == 'critical' else "🟠" if alert['severity'] == 'high' else "🟡"
                    
                    st.markdown(f"""
                    <div class="alert-box {severity_class}">
                        <h4>{icon} {alert['title']}</h4>
                        <p>{alert['message']}</p>
                        <small>{alert['created_at']}</small>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    if not alert['is_read']:
                        if st.button(f"Mark as read", key=f"alert_{alert['id']}"):
                            self.make_api_request(f"/alerts/{alert['id']}/read", method="PUT")
                            st.rerun()
            else:
                st.info("No alerts for this brand")
        else:
            st.info("No alerts found")
    
    def display_reports_tab(self, brand: Dict):
        """Display reports section"""
        st.subheader("📊 Reports & Export")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("### Generate Report")
            
            report_type = st.selectbox(
                "Report Type",
                ["Weekly Summary", "Monthly Analysis", "Competitor Comparison", "Custom Range"]
            )
            
            if report_type == "Custom Range":
                date_range = st.date_input(
                    "Select Date Range",
                    value=(datetime.now() - timedelta(days=7), datetime.now())
                )
            
            format_type = st.selectbox(
                "Export Format",
                ["PDF", "Excel", "CSV"]
            )
            
            if st.button("Generate Report", type="primary"):
                with st.spinner("Generating report..."):
                    # Simulate report generation
                    import time
                    time.sleep(2)
                    st.success("Report generated successfully!")
                    st.download_button(
                        label="Download Report",
                        data=b"Sample report data",
                        file_name=f"{brand['name']}_report_{datetime.now().strftime('%Y%m%d')}.{format_type.lower()}",
                        mime="application/octet-stream"
                    )
        
        with col2:
            st.markdown("### Quick Actions")
            
            if st.button("📧 Email Report", use_container_width=True):
                st.info("Report will be sent to your registered email")
            
            if st.button("📅 Schedule Reports", use_container_width=True):
                st.info("Configure automated report scheduling in settings")
            
            if st.button("📈 Export Raw Data", use_container_width=True):
                # Export raw data
                data = {
                    'brand': brand['name'],
                    'date': datetime.now().isoformat(),
                    'metrics': {
                        'visibility': 75,
                        'sentiment': 60,
                        'mentions': 150
                    }
                }
                st.download_button(
                    label="Download JSON",
                    data=json.dumps(data, indent=2),
                    file_name=f"{brand['name']}_data_{datetime.now().strftime('%Y%m%d')}.json",
                    mime="application/json"
                )
    
    def run_scan(self):
        """Trigger a brand scan"""
        brand = st.session_state.selected_brand
        if not brand:
            st.error("Please select a brand first")
            return
        
        with st.spinner("Starting scan..."):
            response = self.make_api_request(
                f"/brands/{brand['id']}/scan",
                method="POST",
                data={
                    "brand_id": brand['id'],
                    "platforms": ["chatgpt", "claude", "perplexity"],
                    "num_queries": 5
                }
            )
            
            if response:
                st.success("Scan started successfully! Check back in a few minutes for results.")
            else:
                st.error("Failed to start scan")
    
    def generate_report(self):
        """Generate a report"""
        brand = st.session_state.selected_brand
        if not brand:
            st.error("Please select a brand first")
            return
        
        st.info("Report generation started. You'll receive an email when it's ready.")
    
    def run(self):
        """Main app entry point"""
        if not st.session_state.authenticated:
            self.login_page()
        else:
            self.main_dashboard()

# Run the app
if __name__ == "__main__":
    app = BrandMonitorDashboard()
    app.run()
Nie zatrzymuj się, kontynuuj do kroku 8.

KROK 8: IMPLEMENTACJA MODUŁU OPTYMALIZACJI
Zadanie: Stwórz system rekomendacji i optymalizacji widoczności marki.
Plik optimization/recommendation_engine.py:
from typing import List, Dict, Tuple
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime, timedelta
import re

class RecommendationEngine:
    """Generate actionable recommendations for brand visibility improvement"""
    
    def __init__(self):
        self.recommendation_templates = {
            'content_gaps': {
                'title': 'Content Gap Identified',
                'template': 'AI lacks information about {topic}. Consider creating content about {suggestion}.',
                'priority': 'high'
            },
            'keyword_optimization': {
                'title': 'Keyword Optimization Opportunity',
                'template': 'The keyword "{keyword}" appears frequently with competitors but rarely with your brand. Include it in your content strategy.',
                'priority': 'medium'
            },
            'sentiment_improvement': {
                'title': 'Sentiment Improvement Needed',
                'template': 'Negative sentiment detected around {topic}. Address concerns by {action}.',
                'priority': 'high'
            },
            'competitive_positioning': {
                'title': 'Competitive Positioning',
                'template': 'Competitors are mentioned more frequently for {category}. Strengthen your position by {strategy}.',
                'priority': 'medium'
            },
            'authority_building': {
                'title': 'Build Authority',
                'template': 'Increase authority in {area} by {method}.',
                'priority': 'medium'
            },
            'visibility_boost': {
                'title': 'Visibility Opportunity',
                'template': 'Your brand has low visibility for "{query_type}" queries. {action}',
                'priority': 'high'
            }
        }
    
    def generate_recommendations(self, brand_data: Dict, analytics_data: Dict, 
                               mentions_data: List[Dict], competitor_data: Dict) -> List[Dict]:
        """Generate comprehensive recommendations based on all available data"""
        recommendations = []
        
        # Analyze content gaps
        content_gaps = self._identify_content_gaps(mentions_data, brand_data)
        recommendations.extend(content_gaps)
        
        # Keyword optimization
        keyword_recs = self._analyze_keyword_opportunities(mentions_data, competitor_data)
        recommendations.extend(keyword_recs)
        
        # Sentiment-based recommendations
        sentiment_recs = self._analyze_sentiment_issues(mentions_data, analytics_data)
        recommendations.extend(sentiment_recs)
        
        # Competitive positioning
        competitive_recs = self._analyze_competitive_position(analytics_data, competitor_data)
        recommendations.extend(competitive_recs)
        
        # Authority building
        authority_recs = self._suggest_authority_building(analytics_data, mentions_data)
        recommendations.extend(authority_recs)
        
        # Visibility improvements
        visibility_recs = self._suggest_visibility_improvements(analytics_data, mentions_data)
        recommendations.extend(visibility_recs)
        
        # Sort by priority and score
        recommendations.sort(key=lambda x: (
            {'high': 3, 'medium': 2, 'low': 1}[x['priority']],
            x.get('impact_score', 0)
        ), reverse=True)
        
        return recommendations[:10]  # Return top 10 recommendations
    
    def _identify_content_gaps(self, mentions: List[Dict], brand_data: Dict) -> List[Dict]:
        """Identify topics where AI lacks information about the brand"""
        recommendations = []
        
        # Analyze questions that received generic or competitor-focused responses
        no_mention_queries = []
        competitor_dominant_queries = []
        
        for mention in mentions:
            if not mention.get('is_primary_focus', False):
                query_text = mention.get('query_text', '')
                if brand_data['name'].lower() not in mention.get('response_text', '').lower():
                    no_mention_queries.append(query_text)
                elif mention.get('competitor_mentions'):
                    competitor_dominant_queries.append(query_text)
        
        # Extract common themes from queries with no mentions
        if no_mention_queries:
            common_topics = self._extract_common_topics(no_mention_queries)
            for topic, count in common_topics[:3]:
                recommendations.append({
                    'type': 'content_gaps',
                    'title': self.recommendation_templates['content_gaps']['title'],
                    'description': self.recommendation_templates['content_gaps']['template'].format(
                        topic=topic,
                        suggestion=f"your {topic} offerings, features, and unique value propositions"
                    ),
                    'priority': 'high',
                    'impact_score': count * 10,
                    'action_items': [
                        f"Create detailed content about your {topic}",
                        f"Publish case studies featuring {topic}",
                        f"Update website with {topic} information",
                        "Ensure content is indexed by search engines"
                    ]
                })
        
        return recommendations
    
    def _analyze_keyword_opportunities(self, mentions: List[Dict], competitor_data: Dict) -> List[Dict]:
        """Identify keywords associated with competitors but not the brand"""
        recommendations = []
        
        # Extract keywords from competitor contexts
        competitor_keywords = defaultdict(int)
        brand_keywords = defaultdict(int)
        
        for comp_name, comp_info in competitor_data.items():
            for context in comp_info.get('contexts', []):
                keywords = self._extract_keywords(context)
                for keyword in keywords:
                    competitor_keywords[keyword] += 1
        
        # Extract keywords from brand mentions
        for mention in mentions:
            keywords = self._extract_keywords(mention.get('context', ''))
            for keyword in keywords:
                brand_keywords[keyword] += 1
        
        # Find keywords more common with competitors
        keyword_gaps = []
        for keyword, comp_count in competitor_keywords.items():
            brand_count = brand_keywords.get(keyword, 0)
            if comp_count > brand_count * 2 and comp_count >= 3:
                keyword_gaps.append((keyword, comp_count - brand_count))
        
        # Sort by gap size
        keyword_gaps.sort(key=lambda x: x[1], reverse=True)
        
        for keyword, gap in keyword_gaps[:5]:
            recommendations.append({
                'type': 'keyword_optimization',
                'title': self.recommendation_templates['keyword_optimization']['title'],
                'description': self.recommendation_templates['keyword_optimization']['template'].format(
                    keyword=keyword
                ),
                'priority': 'medium',
                'impact_score': gap * 5,
                'action_items': [
                    f'Include "{keyword}" in your product descriptions',
                    f'Create content targeting "{keyword}"',
                    f'Use "{keyword}" in marketing materials',
                    'Monitor performance after implementation'
                ]
            })
        
        return recommendations
    
    def _analyze_sentiment_issues(self, mentions: List[Dict], analytics: Dict) -> List[Dict]:
        """Identify and address sentiment issues"""
        recommendations = []
        
        # Find negative sentiment patterns
        negative_mentions = [m for m in mentions if m.get('sentiment_score', 0) < -0.3]
        
        if negative_mentions:
            # Extract common themes from negative mentions
            negative_contexts = [m.get('context', '') for m in negative_mentions]
            common_issues = self._extract_common_topics(negative_contexts)
            
            for issue, count in common_issues[:2]:
                recommendations.append({
                    'type': 'sentiment_improvement',
                    'title': self.recommendation_templates['sentiment_improvement']['title'],
                    'description': self.recommendation_templates['sentiment_improvement']['template'].format(
                        topic=issue,
                        action=f"publishing positive content, addressing customer concerns, and highlighting improvements"
                    ),
                    'priority': 'high',
                    'impact_score': count * 15,
                    'action_items': [
                        f'Address concerns about {issue} publicly',
                        f'Publish success stories related to {issue}',
                        'Engage with customer feedback',
                        'Monitor sentiment changes over time'
                    ]
                })
        
        # Overall sentiment improvement if needed
        if analytics.get('sentiment_score', 0) < 20:
            recommendations.append({
                'type': 'sentiment_improvement',
                'title': 'Overall Sentiment Enhancement',
                'description': 'Your brand sentiment is below optimal levels. Focus on positive storytelling and customer success.',
                'priority': 'high',
                'impact_score': 80,
                'action_items': [
                    'Launch customer testimonial campaign',
                    'Highlight positive reviews and ratings',
                    'Share success stories on social media',
                    'Respond to negative feedback constructively'
                ]
            })
        
        return recommendations
    
    def _analyze_competitive_position(self, analytics: Dict, competitor_data: Dict) -> List[Dict]:
        """Analyze competitive positioning and suggest improvements"""
        recommendations = []
        
        # Check share of voice
        sov = analytics.get('share_of_voice', 0)
        if sov < 25:  # Less than 25% share of voice
            top_competitors = sorted(
                competitor_data.items(),
                key=lambda x: x[1].get('mention_count', 0),
                reverse=True
            )[:3]
            
            recommendations.append({
                'type': 'competitive_positioning',
                'title': self.recommendation_templates['competitive_positioning']['title'],
                'description': self.recommendation_templates['competitive_positioning']['template'].format(
                    category='general queries',
                    strategy='creating comparison content, highlighting unique features, and improving SEO'
                ),
                'priority': 'high',
                'impact_score': 90,
                'action_items': [
                    'Create "vs competitor" comparison pages',
                    'Highlight unique selling propositions',
                    'Improve technical SEO for AI crawling',
                    'Increase content publication frequency'
                ]
            })
        
        # Analyze specific areas where competitors dominate
        for comp_name, comp_info in competitor_data.items():
            if comp_info.get('comparison_wins', 0) > comp_info.get('comparison_losses', 0) * 2:
                recommendations.append({
                    'type': 'competitive_positioning',
                    'title': f'Competitive Challenge: {comp_name}',
                    'description': f'{comp_name} is frequently preferred in comparisons. Analyze their strengths and differentiate your offering.',
                    'priority': 'medium',
                    'impact_score': 70,
                    'action_items': [
                        f'Research {comp_name} positioning strategy',
                        'Identify and communicate differentiators',
                        'Address specific comparison points',
                        'Update competitive battle cards'
                    ]
                })
                break  # Only one competitor-specific recommendation
        
        return recommendations
    
    def _suggest_authority_building(self, analytics: Dict, mentions: List[Dict]) -> List[Dict]:
        """Suggest ways to build authority"""
        recommendations = []
        
        authority_score = analytics.get('authority_score', 0)
        
        if authority_score < 60:
            # Analyze what makes brands authoritative
            authority_indicators = {
                'thought_leadership': 'publishing industry insights and research',
                'expertise': 'showcasing technical expertise and innovation',
                'trust': 'highlighting certifications, awards, and partnerships',
                'experience': 'emphasizing years of experience and success stories'
            }
            
            # Pick most relevant area based on current mentions
            mention_texts = ' '.join([m.get('context', '') for m in mentions])
            
            for area, method in authority_indicators.items():
                if area not in mention_texts.lower():
                    recommendations.append({
                        'type': 'authority_building',
                        'title': self.recommendation_templates['authority_building']['title'],
                        'description': self.recommendation_templates['authority_building']['template'].format(
                            area=area.replace('_', ' '),
                            method=method
                        ),
                        'priority': 'medium',
                        'impact_score': 60,
                        'action_items': [
                            f'Develop {area.replace("_", " ")} content strategy',
                            'Partner with industry publications',
                            'Participate in relevant conferences',
                            'Create educational resources'
                        ]
                    })
                    break
        
        return recommendations
    
    def _suggest_visibility_improvements(self, analytics: Dict, mentions: List[Dict]) -> List[Dict]:
        """Suggest visibility improvements based on query performance"""
        recommendations = []
        
        visibility_score = analytics.get('visibility_score', 0)
        
        if visibility_score < 70:
            # Analyze query types with low visibility
            query_type_performance = defaultdict(lambda: {'total': 0, 'mentions': 0})
            
            for mention in mentions:
                query_type = mention.get('query_type', 'general')
                query_type_performance[query_type]['total'] += 1
                if mention.get('is_primary_focus', False):
                    query_type_performance[query_type]['mentions'] += 1
            
            # Find underperforming query types
            for query_type, performance in query_type_performance.items():
                if performance['total'] > 0:
                    mention_rate = performance['mentions'] / performance['total']
                    if mention_rate < 0.5:
                        action = self._get_visibility_action(query_type)
                        recommendations.append({
                            'type': 'visibility_boost',
                            'title': self.recommendation_templates['visibility_boost']['title'],
                            'description': self.recommendation_templates['visibility_boost']['template'].format(
                                query_type=query_type.replace('_', ' '),
                                action=action
                            ),
                            'priority': 'high',
                            'impact_score': 85,
                            'action_items': [
                                f'Optimize content for {query_type} queries',
                                'Create FAQ sections addressing common questions',
                                'Improve internal linking structure',
                                'Submit updated sitemap to search engines'
                            ]
                        })
                        break
        
        return recommendations
    
    def _extract_common_topics(self, texts: List[str]) -> List[Tuple[str, int]]:
        """Extract common topics from a list of texts"""
        # Simple noun phrase extraction
        topics = []
        for text in texts:
            # Extract potential topics (simplified - in production use NLP)
            words = re.findall(r'\b[a-z]+(?:\s+[a-z]+)?\b', text.lower())
            topics.extend([w for w in words if len(w) > 4])
        
        topic_counts = Counter(topics)
        return topic_counts.most_common(10)
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Remove common words and extract meaningful terms
        stopwords = {'the', 'is', 'at', 'which', 'on', 'and', 'a', 'an', 'as', 'are', 'was', 'were', 'been', 'be', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'could', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'what', 'which', 'who', 'when', 'where', 'why', 'how', 'all', 'each', 'every', 'some', 'few', 'more', 'most', 'other', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'to', 'from', 'up', 'down', 'in', 'out', 'off', 'over', 'under', 'again', 'further', 'then', 'once'}
        
        words = re.findall(r'\b[a-z]+\b', text.lower())
        keywords = [w for w in words if w not in stopwords and len(w) > 3]
        
        return keywords
    
    def _get_visibility_action(self, query_type: str) -> str:
        """Get specific action based on query type"""
        actions = {
            'product_comparison': 'Create detailed comparison guides and feature matrices',
            'brand_reputation': 'Publish customer testimonials and case studies',
            'purchase_intent': 'Optimize product pages with clear value propositions',
            'customer_service': 'Highlight support options and response times',
            'innovation': 'Showcase R&D efforts and product roadmap',
            'general': 'Improve overall content coverage and SEO'
        }
        
        return actions.get(query_type, actions['general'])
Plik optimization/content_analyzer.py:
from


### References

1. **How to Build an AI Chatbot in 2025: Step-by-Step Guide**. [https://botpress.com](https://botpress.com/blog/how-to-build-your-own-ai-chatbot)
2. **ChatGPT – Przewodnik po AI i jego zastosowaniach w codziennym życiu**. [https://webwavecms.com](https://webwavecms.com/blog/chat-gpt)
3. **Co to jest Chatbot? Projektowanie chatbota krok po kroku**. [https://thestory.is](https://thestory.is/pl/journal/co-to-chatbot-projektowanie/)
4. **7 life hacków do pracy z ChatGPT, o których mogłeś nie wiedzieć**. [https://blog.ringostat.com](https://blog.ringostat.com/pl/7-life-hackow-do-pracy-z-chatgpt-o-ktorych-mogles-nie-wiedziec/)
5. **ChatGPT w praktyce. Poradnik krok po kroku!**. [https://www.senuto.com](https://www.senuto.com/en/blog/chatgpt-step-by-step/)
6. **Jak wdrożyć live chat w sklepie internetowym i zwiększyć sprzedaż? - widoczni**. [https://widoczni.com](https://widoczni.com/blog/live-chat-e-commerce/)
7. **25 narzędzi do podglądania konkurencji w sieci / Agencja e-marketingowa i e-commerce Ideo Force**. [https://www.ideoforce.pl](https://www.ideoforce.pl/akademia/25-narzedzi-do-podgladania-konkurencji-w-sieci,14.html)
8. **ChatGPT – sztuczna inteligencja, która napisze Ci wypracowanie - ChatGPT po polsku**. [https://chatgpt4.pl](https://chatgpt4.pl/poradniki/chatgpt-sztuczna-inteligencja-ktora-napisze-ci-wypracowanie/)
9. **Jak stworzyć chatbota dla Twojej strony internetowej**. [https://www.groupon.pl](https://www.groupon.pl/merchant/zarzadzanie-firma/narzedzia-internetowe/tworzenie-chatbota-dla-twojej-strony-internetowej)
10. **Live chat i chatboty w sklepie internetowym - dlaczego ich potrzebujesz? Poznaj Smartsupp » Shoper Learn**. [https://www.shoper.pl](https://www.shoper.pl/learn/artykul/live-chat-chatboty-w-sklepie-internetowym)
Hubert Gerasik
<EMAIL>
June 16, 2025
Przestań generować kod, a zrób opis kolejnych funkcji, to co trzeba wdrożyć. Programista będzie wiedział co zrobić.
Opis kolejnych funkcji do wdrożenia w systemie AI Brand Monitor
KROK 10: SYSTEM NOTYFIKACJI I ALERTÓW (kontynuacja)
Webhooks Integration
Programista powinien zaimplementować system webhooków umożliwiający integrację z zewnętrznymi platformami:
- Slack Integration - wysyłanie alertów do kanałów Slack
- Discord Webhooks - powiadomienia dla zespołów używających Discord
- Microsoft Teams - integracja z Teams dla środowisk korporacyjnych
- Custom Webhooks - możliwość dodania własnych endpointów HTTP
- Retry Logic - mechanizm ponawiania w przypadku błędów
- Webhook History - logowanie wszystkich wysłanych webhooków
KROK 11: SYSTEM RAPORTOWANIA I EKSPORTU
Report Generator Module
System generowania zaawansowanych raportów w różnych formatach:
- PDF Reports - profesjonalne raporty z wykresami i analizami
- Excel Export - dane w formacie arkusza kalkulacyjnego z wieloma zakładkami
- CSV Export - surowe dane do dalszej analizy
- PowerPoint Presentations - automatyczne generowanie prezentacji z kluczowymi metrykami
- Custom Templates - możliwość tworzenia własnych szablonów raportów
- Scheduled Reports - automatyczne generowanie i wysyłanie raportów
Data Visualization Engine
Zaawansowane wizualizacje danych:
- Interactive Charts - wykresy reagujące na interakcje użytkownika
- Heatmaps - mapy ciepła pokazujące intensywność wzmianek
- Word Clouds - chmury słów kluczowych
- Trend Lines - predykcje trendów na podstawie danych historycznych
- Comparison Matrices - macierze porównawcze z konkurencją
KROK 12: INTEGRACJE Z ZEWNĘTRZNYMI SYSTEMAMI
CRM Integration Module
Integracja z popularnymi systemami CRM:
- Salesforce Connector - synchronizacja danych o markach z Salesforce
- HubSpot Integration - połączenie z HubSpot CRM
- Custom CRM API - uniwersalny interfejs dla innych CRM
- Lead Scoring - ocena leadów na podstawie widoczności marki
- Contact Enrichment - wzbogacanie kontaktów o dane z monitoringu
Marketing Automation Integration
Połączenie z platformami marketingu automatycznego:
- Mailchimp Integration - segmentacja list na podstawie sentymentu
- ActiveCampaign - triggery automatyzacji bazujące na alertach
- Marketing Cloud - synchronizacja danych dla kampanii
- Custom Workflows - tworzenie własnych przepływów automatyzacji
KROK 13: ZAAWANSOWANE FUNKCJE AI
Predictive Analytics Module
System predykcji wykorzystujący machine learning:
- Trend Prediction - przewidywanie trendów widoczności
- Sentiment Forecasting - prognozowanie zmian sentymentu
- Competitor Movement Prediction - przewidywanie ruchów konkurencji
- Alert Prediction - wczesne ostrzeganie przed problemami
- ROI Prediction - szacowanie zwrotu z inwestycji w optymalizację
Natural Language Generation
Automatyczne generowanie treści:
- Report Summaries - automatyczne streszczenia raportów
- Insight Generation - generowanie wniosków z danych
- Recommendation Explanations - szczegółowe wyjaśnienia rekomendacji
- Email Content - personalizowane treści emaili
- Social Media Posts - propozycje postów na social media
KROK 14: SYSTEM ZARZĄDZANIA UŻYTKOWNIKAMI
Advanced User Management
Rozbudowany system zarządzania użytkownikami:
- Role-Based Access Control (RBAC) - szczegółowe uprawnienia
- Team Management - zarządzanie zespołami i działami
- SSO Integration - Single Sign-On z Google, Microsoft, Okta
- Audit Trail - pełna historia działań użytkowników
- Usage Analytics - analiza wykorzystania systemu przez użytkowników
Billing and Subscription Management
System rozliczeń i subskrypcji:
- Stripe Integration - obsługa płatności kartą
- Usage-Based Billing - rozliczanie na podstawie użycia
- Invoice Generation - automatyczne faktury
- Plan Management - zarządzanie planami subskrypcyjnymi
- Payment History - historia płatności i rozliczeń
KROK 15: OPTYMALIZACJA WYDAJNOŚCI
Performance Optimization
Optymalizacja działania systemu:
- Query Caching - cache'owanie wyników zapytań
- Database Indexing - optymalizacja indeksów bazy danych
- Async Processing - asynchroniczne przetwarzanie ciężkich operacji
- CDN Integration - Content Delivery Network dla zasobów
- Load Testing - testy obciążeniowe i optymalizacja
Scalability Features
Funkcje skalowalności:
- Horizontal Scaling - możliwość dodawania kolejnych serwerów
- Database Sharding - podział bazy danych
- Queue Management - zarządzanie kolejkami zadań
- Auto-scaling - automatyczne skalowanie zasobów
- Multi-region Support - wsparcie dla wielu regionów
KROK 16: BEZPIECZEŃSTWO I COMPLIANCE
Security Enhancements
Dodatkowe zabezpieczenia:
- Data Encryption at Rest - szyfrowanie danych w spoczynku
- API Rate Limiting - ograniczenia na API
- IP Whitelisting - lista dozwolonych IP
- Security Headers - nagłówki bezpieczeństwa HTTP
- Vulnerability Scanning - regularne skanowanie podatności
Compliance Features
Zgodność z regulacjami [1]:
- GDPR Compliance - zgodność z RODO (eksport/usuwanie danych)
- Data Retention Policies - polityki przechowywania danych
- Privacy Controls - kontrola prywatności użytkowników
- Compliance Reports - raporty zgodności
- Data Processing Agreements - umowy przetwarzania danych
KROK 17: MOBILE APP
Mobile Application Development
Aplikacja mobilna dla systemu:
- React Native App - cross-platformowa aplikacja mobilna
- Push Notifications - natywne powiadomienia push
- Offline Mode - tryb offline z synchronizacją
- Biometric Authentication - logowanie odciskiem/Face ID
- Mobile-Optimized Dashboard - dashboard dostosowany do mobile
KROK 18: API PUBLICZNE I DOKUMENTACJA
Public API Development
Publiczne API dla integracji:
- RESTful API v2 - rozszerzone API REST
- GraphQL Endpoint - elastyczne zapytania GraphQL
- Webhook API - API dla webhooków
- Rate Limiting - limity dla zewnętrznych użytkowników
- API Key Management - zarządzanie kluczami API
Developer Documentation
Dokumentacja dla developerów:
- API Documentation - pełna dokumentacja API (Swagger/OpenAPI)
- SDK Libraries - biblioteki SDK dla popularnych języków
- Code Examples - przykłady kodu
- Integration Guides - przewodniki integracji
- Video Tutorials - tutoriale wideo
KROK 19: DEPLOYMENT I DEVOPS
Infrastructure as Code
Infrastruktura jako kod:
- Terraform Configuration - konfiguracja infrastruktury
- Docker Orchestration - Kubernetes/Docker Swarm
- CI/CD Pipeline - GitHub Actions/GitLab CI
- Environment Management - dev/staging/production
- Backup Automation - automatyczne backupy
Monitoring and Logging
System monitorowania:
- Application Monitoring - New Relic/DataDog
- Log Aggregation - ELK Stack (Elasticsearch, Logstash, Kibana)
- Error Tracking - Sentry dla śledzenia błędów
- Uptime Monitoring - monitoring dostępności
- Performance Metrics - metryki wydajności
KROK 20: FINALIZACJA I URUCHOMIENIE
Production Deployment na VPS
Wdrożenie produkcyjne:
1. Server Setup - konfiguracja VPS (Ubuntu 22.04 LTS)
2. Security Hardening - zabezpieczenie serwera
3. SSL Certificate - certyfikat Let's Encrypt
4. Domain Configuration - konfiguracja domeny
5. Backup Strategy - strategia backupów
6. Monitoring Setup - konfiguracja monitoringu
7. Load Balancer - konfiguracja load balancera
Post-Launch Activities
Działania po uruchomieniu:
- User Onboarding - proces wdrażania użytkowników
- Support System - system wsparcia technicznego
- Feedback Collection - zbieranie opinii użytkowników
- Performance Optimization - ciągła optymalizacja
- Feature Roadmap - plan rozwoju funkcjonalności
PODSUMOWANIE TECHNICZNE
System AI Brand Monitor to kompleksowa platforma wymagająca:
- Minimum 16GB RAM na serwerze produkcyjnym
- SSD 500GB dla bazy danych i cache
- Python 3.10+ jako główny język
- PostgreSQL jako główna baza (można zastąpić SQLite dla małych wdrożeń)
- Redis dla cache i kolejek
- Nginx jako reverse proxy
- Certbot dla SSL
Całość powinna być konteneryzowana używając Docker i orkiestrowana przez Docker Compose dla łatwego wdrożenia na VPS. System powinien być skalowalny, bezpieczny i łatwy w utrzymaniu, z pełną dokumentacją techniczną i użytkową.
References
1. Funkcje aplikacji mobilnej - na co zwrócić uwagę?. https://grupa-improve.pl
2. Podstawy aplikacji | App architecture. https://developer.android.com
3. Twój pomysł na aplikację mobilną - jak zacząć? - itCraft blog. https://itcraftapps.com
4. Proces tworzenia aplikacji w pigułce - 6 kluczowych etapów. https://blurify.com
5. Czym są aplikacje mobilne? - Fabryka Marketingu. https://fabrykamarketingu.pl
6. 10 obowiązkowych funkcjonalności Twojej aplikacji mobilnej. https://imakeable.com
7. Jak wygląda proces tworzenia aplikacji: Poznaj kluczowe etapy pracy. https://www.intersynergy.pl
8. Jak Wygląda Cały Proces Tworzenia Aplikacji Od A Do Z? - Modest Programmer. https://modestprogrammer.pl
9. Projektowanie aplikacji mobilnych 11 zasad. https://marcinkordowski.com
10. 5 aplikacji hotelowych, które warto wdrożyć w 2025 r.. https://positivehotel.pl
